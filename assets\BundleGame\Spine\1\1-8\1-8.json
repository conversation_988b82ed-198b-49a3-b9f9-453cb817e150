{"skeleton": {"hash": "paS0J8lC0fw6JlVnaTgYkQsiSDA", "spine": "3.8.99", "x": -75.97, "y": -1.31, "width": 159, "height": 205, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/1-8"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "bone2", "parent": "bone", "length": 92.93, "rotation": 90, "x": 0.36, "y": -0.12}, {"name": "bone3", "parent": "bone2", "length": 70.25, "x": 91.66}, {"name": "bone4", "parent": "bone3", "x": 15.48, "y": 0.24}], "slots": [{"name": "1-8<PERSON><PERSON>", "bone": "bone", "attachment": "1-8<PERSON><PERSON>"}, {"name": "1-8<PERSON>u", "bone": "bone", "attachment": "1-8<PERSON>u"}, {"name": "1-8zuiba1", "bone": "bone4", "attachment": "1-8zuiba1"}], "skins": [{"name": "default", "attachments": {"1-8shenti": {"1-8shenti": {"type": "mesh", "hull": 4, "width": 98, "height": 116, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -1.19, -48.67, 1, 1, 2, -1.19, 49.33, 1, 1, 2, 114.81, 49.33, 1, 1, 2, 114.81, -48.67, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-8tou": {"1-8tou": {"type": "mesh", "hull": 4, "width": 159, "height": 114, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -1.85, -82.67, 1, 1, 3, -1.85, 76.33, 1, 1, 3, 112.15, 76.33, 1, 1, 3, 112.15, -82.67, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-8zuiba1": {"1-8zuiba1": {"type": "mesh", "hull": 4, "width": 26, "height": 11, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-6.33, -13.91, -6.33, 12.09, 4.67, 12.09, 4.67, -13.91], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}, "1-8zuiba2": {"type": "mesh", "hull": 4, "width": 24, "height": 12, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-6.33, -9.91, -6.33, 14.09, 5.67, 14.09, 5.67, -9.91], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}, "1-8zuiba3": {"type": "mesh", "hull": 4, "width": 24, "height": 12, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-6.33, -13.91, -6.33, 10.09, 5.67, 10.09, 5.67, -13.91], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"slots": {"1-8zuiba1": {"attachment": [{"name": "1-8zuiba2"}, {"time": 0.1333, "name": "1-8zuiba1"}, {"time": 0.2667, "name": "1-8zuiba3"}, {"time": 0.6, "name": "1-8zuiba1"}, {"time": 0.8333, "name": "1-8zuiba2"}]}}, "bones": {"bone3": {"rotate": [{"angle": 5.29, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -6.7, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 1, "angle": 5.29}], "translate": [{"x": -0.13, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -2.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -2.12, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1, "x": -0.13}], "scale": [{"x": 0.999, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "x": 1.001, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.982, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 1.001, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 0.982, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1, "x": 0.999}]}}}}}