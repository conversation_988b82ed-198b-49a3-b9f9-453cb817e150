var $9AdInfodefault = require("AdInfo").default;
require("GameCommon_Jy");
var $9EventManager = require("EventManager");
var $9EventType = require("EventType");
var $9AudioManager = require("AudioManager");
var $9BundlePath = require("BundlePath");
var $9GameUtils = require("GameUtils");
var $9Utils = require("Utils");
cc.Class({
  extends: cc.Component,
  properties: {
    bg_1: cc.Node,
    bg_2: cc.Node,
    roleParent_1: cc.Node,
    roleParent_2: cc.Node,
    triggerParent: cc.Node,
    btns_2: cc.Node,
    btns_1: cc.Node,
    sBtn_20: cc.Node,
    btnCheiHuanArr: {
      default: [],
      type: [cc.SpriteFrame]
    },
    transNode: cc.Node,
    currentSceneIndex: 1,
    triggerIndex: 0,
    audioIndexArr: [],
    musicRoleList1: [],
    musicRoleList2: []
  },
  onLoad: function () {
    this.audioIndexArr = new Array();
    for (var e = 0; e < this.roleParent_1.childrenCount; e++) {
      var t = this.roleParent_1.children[e].getComponent("MusicRole_Jsc");
      if (t) {
        $9GameUtils.showAnimation(t.node, e);
        this.musicRoleList1.push(t);
      }
    }
    for (e = 0; e < this.roleParent_2.childrenCount; e++) {
      var i = this.roleParent_2.children[e].getComponent("MusicRole_Jsc");
      if (i) {
        $9GameUtils.showAnimation(i.node, e);
        this.musicRoleList2.push(i);
      }
    }
  },
  onEnable: function () {
    $9EventManager.on($9EventType.JUDGEISCANBIANSHEN_3, this.judgeIsCanBianShen, this);
    $9EventManager.on($9EventType.BIANSHEN_3, this.roleBianShen, this);
    $9EventManager.on($9EventType.RESETALLIDELIMG_3, this.resetAllIdle, this);
    $9EventManager.on($9EventType.SHOWBTN_1_3, this.showBtns_1, this);
    $9EventManager.on($9EventType.SHOWBTN_2_3, this.showBtns_2, this);
    $9EventManager.on($9EventType.STOPAUDIO_3, this.stopSound, this);
    $9EventManager.on($9EventType.OPENAUDIO, this.openAudio, this);
    $9EventManager.on($9EventType.CLEARARR, this.clear, this);
    this.initData();
    $9GameUtils.isInScene = true;
  },
  onDisable: function () {
    $9EventManager.off($9EventType.JUDGEISCANBIANSHEN_3, this.judgeIsCanBianShen, this);
    $9EventManager.off($9EventType.BIANSHEN_3, this.roleBianShen, this);
    $9EventManager.off($9EventType.RESETALLIDELIMG_3, this.resetAllIdle, this);
    $9EventManager.off($9EventType.SHOWBTN_1_3, this.showBtns_1, this);
    $9EventManager.off($9EventType.SHOWBTN_2_3, this.showBtns_2, this);
    $9EventManager.off($9EventType.STOPAUDIO_3, this.stopSound, this);
    $9EventManager.off($9EventType.OPENAUDIO, this.openAudio, this);
    $9EventManager.off($9EventType.CLEARARR, this.clear, this);
  },
  start: function () {},
  initData: function () {
    this.currentSceneIndex = 1;
    this.triggerIndex = 0;
    this.needBianShenNode = null;
    this.changeRoleParent();
    this.transNode.active = false;
    this.transNode.opacity = 0;
    $9GameUtils.curscene = 1;
    for (var e = 0; e < this.btns_1.childrenCount; e++) {
      this.btns_1.children[e].getComponent("DragItem").reFresh();
    }
  },
  changeRoleParent: function () {
    this.changeBg(this.currentSceneIndex);
    this.roleParent_1.active = false;
    this.roleParent_2.active = false;
    if (1 == this.currentSceneIndex) {
      this.roleParent_1.active = true;
    } else {
      2 == this.currentSceneIndex && (this.roleParent_2.active = true);
    }
    this.showRoleParent(0, 0, true);
    this.showBtns_2(-1, false);
    this.showBtns_1(-1);
  },
  changeBg: function () {},
  showRoleParent: function (e, t, i) {
    if (i) {
      if (1 == this.currentSceneIndex) {
        for (var n = 0; n < this.musicRoleList1.length; n++) {
          var s = this.musicRoleList1[n];
          if (s) {
            s.changeRoleState(0);
            s.initData(n);
          }
        }
      } else {
        for (n = 0; n < this.musicRoleList2.length; n++) {
          var o = this.musicRoleList2[n];
          if (o) {
            o.changeRoleState(0);
            o.initData(n);
          }
        }
      }
    } else if (1 == this.currentSceneIndex) {
      for (n = 0; n < this.musicRoleList1.length; n++) {
        if (e == n) {
          var r = this.musicRoleList1[n];
          r && r.changeRoleState(t);
          break;
        }
      }
    } else {
      for (n = 0; n < this.musicRoleList2.length; n++) {
        if (e == n) {
          var c = this.musicRoleList2[n];
          c && c.changeRoleState(t);
          break;
        }
      }
    }
  },
  showBtns_2: function (e, t) {
    if (-1 == e) {
      for (var i = 0; i < this.btns_2.childrenCount; i++) {
        this.btns_2.children[i].active = false;
      }
    } else {
      for (var n = 0; n < this.btns_2.childrenCount; n++) {
        if (e == n) {
          this.btns_2.children[n].active = !t;
          break;
        }
      }
    }
  },
  showBtns_1: function (e) {
    if (-1 == e) {
      for (var t = 0; t < this.btns_1.childrenCount; t++) {
        this.btns_1.children[t].active = true;
        this.btns_1.children[t].children[0] && (this.btns_1.children[t].children[0].getComponent(cc.Sprite).spriteFrame = this.btns_1.children[t].getComponent(cc.Sprite).spriteFrame);
      }
      if (1 == this.currentSceneIndex) {
        this.sBtn_20.getComponent(cc.Sprite).SpriteFrame = this.btnCheiHuanArr[0];
      } else {
        this.sBtn_20.getComponent(cc.Sprite).SpriteFrame = this.btnCheiHuanArr[1];
      }
    } else {
      for (var i = 0; i < this.btns_1.childrenCount; i++) {
        if (e == i) {
          this.btns_1.children[i].active = true;
          this.btns_1.children[i].getComponent("DragItem").reFresh();
          break;
        }
      }
    }
  },
  judgeIsCanBianShen: function (e) {
    if (1 == this.currentSceneIndex) {
      for (var t = this.musicRoleList1.length - 1; t >= 0; t--) {
        var i = this.musicRoleList1[t];
        if ($9Utils.cheakCollierBox(e, this.triggerParent.children[t]) && !i.getIsCanClick()) {
          $9GameUtils.isCanInstall = true;
          this.triggerIndex = $9Utils.getTouchNum(e.name);
          this.needBianShenNode = this.musicRoleList1[t].node;
          i.changeRoleColor(true);
          this.resetAllIdle(t);
          break;
        }
        $9GameUtils.isCanInstall = false;
        this.triggerIndex = 0;
      }
    } else {
      for (var n = this.musicRoleList2.length - 1; n >= 0; n--) {
        var s = this.musicRoleList2[n];
        if ($9Utils.cheakCollierBox(e, this.triggerParent.children[n]) && !s.getIsCanClick()) {
          $9GameUtils.isCanInstall = true;
          this.triggerIndex = $9Utils.getTouchNum(e.name);
          this.needBianShenNode = this.musicRoleList2[n].node;
          s.changeRoleColor(true);
          this.resetAllIdle(n);
          break;
        }
        $9GameUtils.isCanInstall = false;
        this.triggerIndex = 0;
      }
    }
  },
  resetAllIdle: function (e) {
    if (1 == this.currentSceneIndex) {
      for (var t = 0; t < this.musicRoleList1.length; t++) {
        e != t && this.musicRoleList1[t].changeRoleColor(false);
      }
    } else if (2 == this.currentSceneIndex) {
      for (var i = 0; i < this.musicRoleList2.length; i++) {
        e != i && this.musicRoleList2[i].changeRoleColor(false);
      }
    }
  },
  roleBianShen: function () {
    var e = this;
    var t = this;
    if (0 != this.triggerIndex && $9GameUtils.isCanInstall) {
      var i = this.needBianShenNode.getComponent("MusicRole_Jsc");
      if (20 == this.triggerIndex && 1 == this.currentSceneIndex) {
        this.currentSceneIndex = 2;
        this.transScene(function () {
          $9AudioManager.stopAllAudio();
          t.changeRoleParent();
          t.showBtns_2(t.triggerIndex - 1, false);
          var i = $9Utils.getTouchNum(t.needBianShenNode.name);
          var n = t.roleParent_2.children[i - 1].getComponent("MusicRole_Jsc");
          if (n) {
            n.changeRoleState(e.triggerIndex);
            n.setClickState(e.triggerIndex);
          }
          t.triggerIndex = 0;
          t.needBianShenNode = null;
          e.audioIndexArr.length = 0;
          $9GameUtils.curscene = 2;
          e.btns_1.children[19].active = false;
          for (var s = 0; s < e.btns_1.childrenCount; s++) {
            e.btns_1.children[s].getComponent("DragItem").reFresh();
          }
          "vivo" != globalThis.channel && "oppo" != globalThis.channel || globalThis.AdInfo.adsManager.showInterstitialJudgmentArea();
        }, function () {
          $9AudioManager.playAudio($9BundlePath.JscPath, "Sound/roleK_20", true);
          e.audioIndexArr.push(20);
          e.transNode.active = false;
        }, 0);
        return;
      }
      if (i) {
        i.changeRoleState(this.triggerIndex);
        i.setClickState(this.triggerIndex);
        this.playSound();
        this.audioIndexArr.push(this.triggerIndex);
      }
      this.showBtns_2(this.triggerIndex - 1, false);
      this.triggerIndex = 0;
      this.needBianShenNode = null;
    }
  },
  openAudio: function () {
    if (!(this.audioIndexArr.length <= 0)) {
      for (var e = 0; e < this.audioIndexArr.length; e++) {
        if (1 == this.currentSceneIndex) {
          $9AudioManager.playAudio($9BundlePath.JscPath, "Sound/roleS_" + this.audioIndexArr[e], true);
        } else {
          $9AudioManager.playAudio($9BundlePath.JscPath, "Sound/roleK_" + this.audioIndexArr[e], true);
        }
      }
    }
  },
  playSound: function () {
    if (1 == this.currentSceneIndex) {
      $9AudioManager.playAudio($9BundlePath.JscPath, "Sound/roleS_" + this.triggerIndex, true);
    } else {
      $9AudioManager.playAudio($9BundlePath.JscPath, "Sound/roleK_" + this.triggerIndex, true);
    }
  },
  stopSound: function (e) {
    if (1 == this.currentSceneIndex) {
      $9AudioManager.stopOneAudio("Sound/roleS_" + e);
    } else {
      $9AudioManager.stopOneAudio("Sound/roleK_" + e);
    }
    for (var t = 0; t < this.audioIndexArr.length; t++) {
      if (this.audioIndexArr[t] == e) {
        this.audioIndexArr.splice(t, 1);
        break;
      }
    }
  },
  transScene: function (e, t, i) {
    this.transNode.opacity = 0;
    this.transNode.active = true;
    cc.tween(this.transNode).delay(i).to(.5, {
      opacity: 255
    }).call(function () {
      e && e();
    }).to(1, {
      opacity: 0
    }).call(function () {
      t && t();
    }).start();
  },
  rePlay: function () {
    var e = this;
    $9AdInfodefault.adsManager.showInterstitial();
    var t = this;
    this.transScene(function () {
      t.triggerIndex = 0;
      t.needBianShenNode = null;
      $9AudioManager.stopAllAudio();
      t.changeRoleParent();
      e.clear();
    }, function () {
      e.transNode.active = false;
    }, 0);
  },
  clear: function () {
    this.audioIndexArr.length = 0;
  },
  backHome: function () {
    var e = this;
    $9GameUtils.back(function () {
      e.node.destroy();
      e.clear();
      $9AudioManager.stopAllAudio();
      $9GameUtils.isInGame = false;
      "vivo" != globalThis.channel && "oppo" != globalThis.channel || globalThis.AdInfo.adsManager.showInterstitialJudgmentArea();
      $9GameUtils.curscene = 1;
    });
  }
});