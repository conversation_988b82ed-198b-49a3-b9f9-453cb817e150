var $9EventManager = require("EventManager");
var $9EventType = require("EventType");
var $9GameUtils = require("GameUtils");
cc.Class({
  extends: cc.Component,
  properties: {
    state_0: cc.Node,
    qiehuanSpr: {
      default: [],
      type: [cc.SpriteFrame]
    },
    isCanClick: false,
    posY_Pre: 0,
    hasBianShenState: 0,
    endPosY: -530
  },
  onLoad: function () {
    this.posY_Pre = this.node.y;
  },
  onEnable: function () {
    this.node.on(cc.Node.EventType.TOUCH_START, this.restoreRole, this);
  },
  onDisable: function () {
    this.node.off(cc.Node.EventType.TOUCH_START, this.restoreRole, this);
  },
  start: function () {},
  initData: function () {
    this.isCanClick = false;
    this.hasBianShenState = 0;
    this.changeRoleColor(false);
  },
  changeRoleColor: function (t) {
    this.state_0.getComponent(cc.Sprite).spriteFrame = t ? this.qiehuanSpr[1] : this.qiehuanSpr[0];
  },
  restoreRole: function () {
    var t = this;
    var i = this.hasBianShenState;
    if (this.isCanClick) {
      $9GameUtils.isCanDragMusicBtn = false;
      this.isCanClick = false;
      $9EventManager.emit($9EventType.STOPAUDIO_4, this.hasBianShenState);
      cc.tween(this.node).to(.5, {
        position: cc.v2(this.node.x, this.endPosY)
      }).call(function () {
        t.changeRoleState(0);
        t.changeRoleColor(false);
      }).to(.3, {
        position: cc.v2(this.node.x, this.node.y)
      }).call(function () {
        $9EventManager.emit($9EventType.SHOWBTN_1_4, i - 1);
        $9EventManager.emit($9EventType.SHOWBTN_2_4, i - 1, true);
        $9GameUtils.isCanDragMusicBtn = true;
      }).start();
    }
  },
  changeRoleState: function (t) {
    for (var i = 0; i < this.node.childrenCount; i++) {
      this.node.children[i].active = t == i;
    }
  },
  setClickState: function (t) {
    this.hasBianShenState = t;
    this.isCanClick = true;
  },
  getIsCanClick: function () {
    return this.isCanClick;
  }
});