{"skeleton": {"hash": "QmXC1p8DYvIWTCQaO8U0rMaFAig", "spine": "3.8.99", "x": -64.27, "y": -2.41, "width": 131.64, "height": 205.52, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/1-3"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "scaleX": 0.6716, "scaleY": 0.6716}, {"name": "bone2", "parent": "bone", "length": 149.98, "rotation": 90, "x": 0.28, "y": 0.28}, {"name": "bone3", "parent": "bone2", "length": 91.6, "x": 149.25, "y": 0.38}, {"name": "bone4", "parent": "bone3", "x": 109.6, "y": -17.72}, {"name": "bone5", "parent": "bone3", "length": 40.93, "rotation": 48.01, "x": 81.46, "y": 53.74}, {"name": "bone6", "parent": "bone3", "length": 42.45, "rotation": -52.77, "x": 81.46, "y": -56.78}, {"name": "bone7", "parent": "bone3", "x": 62.53, "y": -0.34}, {"name": "bone8", "parent": "bone3", "x": 38.53, "y": -0.98}, {"name": "bone9", "parent": "bone4", "length": 32.55, "rotation": 80.71, "x": -0.48, "y": -0.01}, {"name": "bone10", "parent": "bone9", "length": 37.63, "rotation": -12.1, "x": 32.55}], "slots": [{"name": "1-3<PERSON><PERSON>", "bone": "bone", "attachment": "1-3<PERSON><PERSON>"}, {"name": "1-3<PERSON><PERSON>", "bone": "bone", "attachment": "1-3<PERSON><PERSON>"}, {"name": "1-3zuiba", "bone": "bone", "attachment": "1-3zuiba"}, {"name": "1-3<PERSON><PERSON><PERSON>", "bone": "bone", "attachment": "1-3<PERSON><PERSON><PERSON>"}, {"name": "1-3biaoqing", "bone": "bone", "attachment": "1-3biaoqing"}], "skins": [{"name": "default", "attachments": {"1-3biaoqing": {"1-3biaoqing": {"type": "mesh", "hull": 4, "width": 77, "height": 43, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 7, -14.65, -42.07, 1, 1, 7, -14.65, 34.93, 1, 1, 7, 28.35, 34.93, 1, 1, 7, 28.35, -42.07, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-3shenti": {"1-3shenti": {"type": "mesh", "hull": 4, "width": 133, "height": 211, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -3.87, -69.03, 1, 1, 2, -3.87, 63.97, 1, 1, 2, 207.13, 63.97, 1, 1, 2, 207.13, -69.03, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-3tou": {"1-3tou": {"type": "mesh", "hull": 21, "width": 196, "height": 124, "uvs": [1, 0.14065, 1, 0.25581, 0.9706, 0.40823, 0.8881, 0.48783, 0.92989, 0.64194, 0.88274, 0.84178, 0.7156, 1, 0.33237, 1, 0.12666, 0.8843, 0.07737, 0.6577, 0.10737, 0.49512, 0, 0.34439, 0, 0.19197, 0.0463, 0.0531, 0.13951, 0, 0.24344, 0, 0.33558, 0.08866, 0.4888, 0.03414, 0.67201, 0.09002, 0.7606, 0, 0.90417, 0, 0.1588, 0.38503, 0.2638, 0.19197, 0.74667, 0.21517, 0.82381, 0.3913], "triangles": [20, 0, 1, 20, 23, 19, 1, 23, 20, 2, 24, 1, 21, 11, 12, 22, 14, 15, 13, 14, 21, 21, 12, 13, 23, 18, 19, 5, 24, 3, 5, 3, 4, 22, 15, 16, 8, 9, 7, 17, 7, 22, 17, 22, 16, 17, 23, 7, 6, 7, 23, 10, 7, 9, 21, 22, 7, 7, 10, 21, 23, 17, 18, 6, 23, 24, 6, 24, 5, 22, 21, 14, 10, 11, 21, 1, 24, 23, 3, 24, 2], "vertices": [2, 3, 104.44, -100.41, 0.00012, 6, 48.64, -8.1, 0.99988, 2, 3, 90.16, -100.41, 0.00029, 6, 40, -19.47, 0.99971, 2, 3, 71.26, -94.65, 0.33362, 6, 23.98, -31.03, 0.66638, 1, 3, 61.39, -78.48, 1, 1, 3, 42.28, -86.67, 1, 1, 3, 17.5, -77.43, 1, 1, 3, -2.12, -44.67, 1, 1, 3, -2.12, 30.45, 1, 1, 3, 12.23, 70.77, 1, 1, 3, 40.33, 80.43, 1, 1, 3, 60.49, 74.55, 1, 2, 3, 79.18, 95.59, 0.33333, 5, 29.58, 29.69, 0.66667, 1, 5, 42.22, 15.64, 1, 2, 3, 115.3, 86.52, 0.00483, 5, 47, -3.23, 0.99517, 2, 3, 121.88, 68.25, 0.04043, 5, 37.82, -20.35, 0.95957, 2, 3, 121.88, 47.88, 0.37377, 5, 22.68, -33.97, 0.62623, 1, 3, 110.89, 29.82, 1, 1, 3, 117.65, -0.21, 1, 1, 3, 110.72, -36.12, 1, 2, 3, 121.88, -53.49, 0.35778, 6, 21.84, 34.18, 0.64222, 2, 3, 121.88, -81.63, 0.02445, 6, 44.24, 17.15, 0.97555, 1, 3, 74.14, 64.47, 1, 1, 3, 98.08, 43.89, 1, 1, 3, 95.2, -50.76, 1, 1, 3, 73.36, -65.88, 1], "edges": [20, 42, 42, 44, 44, 32, 32, 30, 28, 30, 28, 26, 26, 24, 22, 24, 22, 20, 20, 18, 18, 16, 16, 14, 32, 34, 34, 36, 36, 46, 46, 48, 48, 6, 6, 4, 4, 2, 2, 0, 0, 40, 38, 40, 38, 36, 6, 8, 8, 10, 12, 14, 10, 12]}}, "1-3toufa": {"1-3toufa": {"type": "mesh", "hull": 13, "width": 123, "height": 64, "uvs": [0.84795, 0.09716, 1, 0.30497, 1, 0.79184, 0.76917, 1, 0.52665, 1, 0.28104, 0.91653, 0.10186, 0.68497, 0, 0.34059, 0.03852, 0.20997, 0.17755, 0.08528, 0.36755, 0, 0.42779, 0.13575, 0.59307, 0], "triangles": [6, 8, 9, 7, 8, 6, 9, 10, 11, 11, 6, 9, 5, 6, 11, 3, 0, 1, 4, 11, 12, 3, 4, 12, 0, 3, 12, 5, 11, 4, 1, 2, 3], "vertices": [2, 9, -13.65, -40.27, 0.99848, 10, -36.73, -49.06, 0.00152, 1, 9, -34.25, -30.16, 1, 1, 9, -39.29, 0.59, 1, 1, 9, -13.42, 18.32, 1, 2, 9, 16.02, 23.14, 0.98278, 10, -21.01, 19.16, 0.01722, 2, 9, 46.7, 22.74, 0.1833, 10, 9.07, 25.2, 0.8167, 1, 10, 35, 19.44, 1, 1, 10, 54.7, 3.49, 1, 1, 10, 53.34, -6.02, 1, 2, 9, 67.85, -27.7, 0.00492, 10, 40.32, -19.69, 0.99508, 2, 9, 45.67, -36.86, 0.13847, 10, 20.55, -33.3, 0.86153, 2, 9, 36.95, -29.49, 0.36263, 10, 10.49, -27.91, 0.63737, 2, 9, 18.29, -41.34, 0.85743, 10, -5.28, -43.41, 0.14257], "edges": [14, 12, 12, 10, 10, 8, 6, 8, 6, 4, 4, 2, 2, 0, 0, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14]}}, "1-3zuiba": {"1-3zuiba": {"type": "mesh", "hull": 4, "width": 57, "height": 44, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 8, -21.64, -29.43, 1, 1, 8, -21.64, 27.57, 1, 1, 8, 22.36, 27.57, 1, 1, 8, 22.36, -29.43, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"bones": {"bone3": {"rotate": [{"angle": 2.39, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 7.66, "curve": "stepped"}, {"time": 0.2333, "angle": 7.66, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 7.66, "curve": "stepped"}, {"time": 0.7333, "angle": 7.66, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 2.39}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -4.39, "y": 8.78, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -2, "y": 4.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -4.39, "y": 8.78, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -2, "y": 4.39, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "bone7": {"translate": [{"x": 1.96, "y": -0.19, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "x": 2.38, "y": -0.24, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -4.35, "y": 0.58, "curve": "stepped"}, {"time": 0.2667, "x": -4.35, "y": 0.58, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "x": 1.96, "y": -0.19, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.5333, "x": 2.38, "y": -0.24, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -4.35, "y": 0.58, "curve": "stepped"}, {"time": 0.7667, "x": -4.35, "y": 0.58, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1, "x": 1.96, "y": -0.19}]}, "bone8": {"translate": [{"x": -0.07, "y": 0.01, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -1.19, "y": 0.16, "curve": "stepped"}, {"time": 0.2667, "x": -1.19, "y": 0.16, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "x": -0.07, "y": 0.01, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -1.19, "y": 0.16, "curve": "stepped"}, {"time": 0.7667, "x": -1.19, "y": 0.16, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1, "x": -0.07, "y": 0.01}], "scale": [{"x": 1.016, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 1.262, "curve": "stepped"}, {"time": 0.2667, "x": 1.262, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "x": 1.016, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.262, "curve": "stepped"}, {"time": 0.7667, "x": 1.262, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1, "x": 1.016}]}, "bone5": {"rotate": [{"angle": -5.94, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": -8.76, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 12.94, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.5, "angle": -5.94, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5667, "angle": -8.76, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 12.94, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": -5.94}]}, "bone6": {"rotate": [{"angle": 6.76, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": 9.36, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -10.63, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.5, "angle": 6.76, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5667, "angle": 9.36, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -10.63, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": 6.76}]}, "bone4": {"rotate": [{"angle": -10.02, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -6.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 14.34, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -16.68, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5, "angle": -10.02, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5667, "angle": -6.14, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 14.34, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -16.68, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": -10.02}], "translate": [{"x": -0.6, "y": -1.68, "curve": "stepped"}, {"time": 0.0667, "x": -0.6, "y": -1.68, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -0.2, "y": 14.84, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -0.6, "y": -1.68, "curve": "stepped"}, {"time": 0.5667, "x": -0.6, "y": -1.68, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -0.2, "y": 14.84, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -0.6, "y": -1.68}]}, "bone9": {"rotate": [{"angle": 1.31, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 10.37, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5, "angle": 1.31, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.6, "angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 10.37, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 1, "angle": 1.31}]}, "bone10": {"rotate": [{"angle": 3.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 10.37, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": 3.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6333, "angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 10.37, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": 3.55}]}}}}}