{"skeleton": {"hash": "8xo3DejSJNCXrubACK1BCE1x5yY", "spine": "3.8.99", "x": -103.1, "y": -1.96, "width": 201.68, "height": 229.92, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/系列2/2-5"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "scaleX": 0.8067, "scaleY": 0.8067}, {"name": "bone2", "parent": "bone", "length": 127.01, "rotation": 90, "x": -0.85, "y": 0.06}, {"name": "bone3", "parent": "bone2", "length": 84.59, "x": 131.67, "y": 0.02}, {"name": "bone4", "parent": "bone3", "length": 34.91, "rotation": 40.12, "x": 78.41, "y": 43.56}, {"name": "bone5", "parent": "bone3", "length": 31.87, "rotation": -35.04, "x": 83.38, "y": -47.29}, {"name": "bone6", "parent": "bone3", "x": 51.69, "y": -0.18}, {"name": "bone7", "parent": "bone3", "x": 73.94, "y": -0.15}, {"name": "bone8", "parent": "bone3", "x": 5.92, "y": -9.19}, {"name": "bone9", "parent": "bone3", "length": 26.56, "rotation": 125.54, "x": 31.74, "y": 23.97}, {"name": "bone10", "parent": "bone9", "length": 30.31, "rotation": 9.46, "x": 27.55}, {"name": "bone11", "parent": "bone3", "length": 25.21, "rotation": -126.21, "x": 32.11, "y": -21.82}, {"name": "bone12", "parent": "bone11", "length": 34.48, "rotation": -12.21, "x": 26.15}], "slots": [{"name": "2-5<PERSON><PERSON><PERSON>", "bone": "bone", "attachment": "2-5<PERSON><PERSON><PERSON>"}, {"name": "2-5<PERSON><PERSON>", "bone": "bone", "attachment": "2-5<PERSON><PERSON>"}, {"name": "2-5<PERSON><PERSON>", "bone": "bone", "attachment": "2-5<PERSON><PERSON>"}, {"name": "2-5meimao2", "bone": "bone", "attachment": "2-5meimao2"}, {"name": "2-5meimao1", "bone": "bone", "attachment": "2-5meimao1"}, {"name": "2-5yanjin2", "bone": "bone", "attachment": "2-5yanjin2"}, {"name": "2-5yanjin1", "bone": "bone", "attachment": "2-5yanjin1"}], "skins": [{"name": "default", "attachments": {"2-5meimao1": {"2-5meimao1": {"type": "mesh", "hull": 4, "width": 34, "height": 12, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 7, 0.9, 6.08, 1, 1, 7, 0.9, 40.08, 1, 1, 7, 12.9, 40.08, 1, 1, 7, 12.9, 6.08, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-5meimao2": {"2-5meimao2": {"type": "mesh", "hull": 4, "width": 34, "height": 12, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 7, 0.9, -43.92, 1, 1, 7, 0.9, -9.92, 1, 1, 7, 12.9, -9.92, 1, 1, 7, 12.9, -43.92, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-5shenti": {"2-5shenti": {"type": "mesh", "hull": 4, "width": 116, "height": 168, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -2.49, -60.05, 1, 1, 2, -2.49, 55.95, 1, 1, 2, 165.51, 55.95, 1, 1, 2, 165.51, -60.05, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-5tou": {"2-5tou": {"type": "mesh", "hull": 4, "width": 153, "height": 170, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -20.15, -74.07, 1, 1, 3, -20.15, 78.93, 1, 1, 3, 149.85, 78.93, 1, 1, 3, 149.85, -74.07, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-5toufa": {"2-5toufa": {"type": "mesh", "hull": 28, "width": 250, "height": 153, "uvs": [1, 0.21812, 1, 0.30276, 0.92411, 0.29133, 0.95771, 0.50636, 0.86671, 0.48348, 0.89191, 0.70767, 0.76731, 0.65048, 0.65427, 0.75799, 0.58707, 0.89525, 0.55767, 1, 0.49672, 1, 0.47316, 0.88818, 0.45192, 0.78737, 0.37772, 0.71417, 0.32592, 0.77136, 0.21952, 0.78051, 0.17612, 0.68672, 0.13692, 0.50371, 0.14952, 0.28639, 0.07392, 0.22463, 0, 0.27038, 0, 0.16973, 0.06132, 0.0462, 0.17752, 0, 0.39071, 0.04427, 0.57271, 0.2227, 0.72811, 0.05113, 0.92131, 0], "triangles": [25, 13, 17, 24, 18, 23, 25, 17, 24, 6, 7, 25, 8, 9, 11, 9, 10, 11, 7, 8, 12, 8, 11, 12, 7, 12, 25, 25, 12, 13, 25, 26, 4, 6, 4, 5, 6, 25, 4, 4, 2, 3, 4, 26, 2, 2, 0, 1, 26, 27, 2, 2, 27, 0, 19, 22, 23, 15, 16, 14, 14, 16, 13, 13, 16, 17, 24, 17, 18, 18, 19, 23, 20, 21, 19, 21, 22, 19], "vertices": [3, 11, 81.48, 60.1, 0.00555, 12, 41.37, 70.44, 0.88334, 3, 32.47, -123.07, 0.11111, 2, 11, 89.13, 49.65, 0.00282, 12, 51.05, 61.85, 0.99718, 2, 11, 72.79, 39.86, 9e-05, 12, 37.15, 48.82, 0.99991, 2, 11, 99, 18.27, 6e-05, 12, 67.34, 33.26, 0.99994, 3, 11, 78.58, 7.66, 3e-05, 12, 49.62, 18.57, 0.98889, 3, -8.13, -89.74, 0.01108, 4, 10, -59.6, 141.83, 0.00186, 11, 103.92, -16.3, 0, 12, 79.46, 0.52, 0.91329, 3, -42.43, -96.04, 0.08484, 4, 10, -43.76, 113.62, 0.00873, 11, 73.62, -27.64, 0, 12, 52.24, -16.98, 0.75016, 3, -33.68, -64.89, 0.24111, 4, 10, -12.14, 105.27, 0.02081, 11, 60.54, -57.6, 0, 12, 45.79, -49.03, 0.51859, 3, -50.13, -36.63, 0.4606, 4, 10, 14.58, 108.24, 0.03478, 11, 59.39, -84.47, 0, 12, 50.34, -75.54, 0.32487, 3, -71.13, -19.83, 0.64035, 4, 10, 31.11, 114.37, 0.04689, 11, 62.92, -101.75, 0, 12, 57.45, -91.67, 0.21368, 3, -87.15, -12.48, 0.73943, 4, 9, 51.84, 109.08, 0, 10, 41.89, 103.6, 0.06661, 12, 47.34, -103.07, 0.17499, 3, -87.15, 2.75, 0.7584, 4, 9, 46.69, 91.73, 0.00041, 10, 33.96, 87.34, 0.1341, 12, 30.63, -96.12, 0.14596, 3, -70.05, 8.64, 0.71953, 4, 9, 42.05, 76.09, 0.00081, 10, 26.8, 72.68, 0.2731, 12, 15.57, -89.85, 0.10373, 3, -54.62, 13.95, 0.62235, 4, 9, 50.63, 56.2, 0.00122, 10, 32, 51.64, 0.48085, 12, -5.12, -96.3, 0.05503, 3, -43.42, 32.5, 0.4629, 4, 9, 66.25, 55.79, 0.00081, 10, 47.35, 48.67, 0.68496, 12, -7.17, -111.79, 0.0205, 3, -52.17, 45.45, 0.29374, 4, 9, 88.71, 41.47, 0.00053, 10, 67.14, 30.85, 0.8425, 12, -23.77, -132.61, 0.00372, 3, -53.57, 72.05, 0.15325, 4, 9, 89.2, 23.49, 0.03212, 10, 64.67, 13.03, 0.89813, 12, -41.71, -131.21, 0.00043, 3, -39.22, 82.9, 0.06933, 3, 9, 80.9, -4.99, 0.1139, 10, 51.8, -13.7, 0.8622, 3, -11.22, 92.7, 0.0239, 3, 9, 59.01, -30.22, 0.24573, 10, 26.06, -34.98, 0.74805, 3, 22.03, 89.55, 0.00622, 3, 9, 68.9, -48.9, 0.36465, 10, 32.74, -55.03, 0.63529, 3, 31.48, 108.45, 6e-05, 2, 9, 88.01, -53.94, 0.43891, 10, 50.76, -63.15, 0.56109, 2, 9, 79.05, -66.47, 0.48341, 10, 39.87, -74.03, 0.51659, 3, 9, 55.59, -72.94, 0.45814, 10, 15.67, -76.56, 0.43075, 3, 58.78, 111.6, 0.11111, 3, 9, 27.85, -61.81, 0.30209, 10, -9.87, -61.02, 0.25347, 3, 65.85, 82.55, 0.44444, 1, 3, 59.07, 29.26, 1, 1, 3, 31.77, -16.24, 1, 1, 3, 58.02, -55.09, 1, 3, 11, 45.89, 75.41, 0.00552, 12, 3.35, 77.88, 0.55004, 3, 65.85, -103.39, 0.44444], "edges": [42, 44, 44, 46, 40, 42, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 20, 22, 22, 24, 18, 20, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 54, 54, 52, 52, 50, 50, 48, 48, 46]}}, "2-5yanjin1": {"2-5yanjin1": {"type": "mesh", "hull": 17, "width": 75, "height": 120, "uvs": [0.56236, 0.10131, 0.55936, 0.24943, 0.64636, 0.31318, 0.7425, 0.45591, 0.8285, 0.58357, 0.92116, 0.72113, 1, 0.83818, 1, 1, 0.57736, 1, 0.50679, 0.88434, 0.41708, 0.73731, 0.3141, 0.56852, 0.21436, 0.40506, 0.20236, 0.34693, 0, 0.30756, 0, 0, 0.40636, 0, 0.40936, 0.31131], "triangles": [10, 4, 5, 9, 10, 5, 9, 5, 6, 8, 6, 7, 8, 9, 6, 4, 10, 11, 4, 11, 3, 3, 17, 2, 2, 17, 1, 11, 17, 3, 11, 12, 17, 12, 13, 17, 17, 13, 16, 0, 1, 17, 15, 16, 13, 13, 14, 15, 0, 17, 16], "vertices": [1, 4, 44.94, -40.22, 1, 1, 4, 31.49, -28.6, 1, 1, 4, 21.43, -28.66, 1, 2, 4, 3.69, -23.14, 0.88889, 3, 96.14, 28.25, 0.11111, 2, 4, -12.18, -18.19, 0.55556, 3, 80.82, 21.8, 0.44444, 1, 3, 64.31, 14.85, 1, 1, 3, 50.26, 8.93, 1, 1, 3, 30.85, 8.93, 1, 1, 3, 30.85, 40.63, 1, 1, 3, 44.72, 45.92, 1, 2, 4, -6.4, 17.29, 0.55556, 3, 62.37, 52.65, 0.44444, 2, 4, 14.06, 10.14, 0.88889, 3, 82.62, 60.38, 0.11111, 1, 4, 33.88, 3.22, 1, 1, 4, 39.8, -0.58, 1, 1, 4, 53.19, 7.98, 1, 1, 4, 81.41, -15.81, 1, 1, 4, 61.77, -39.11, 1, 1, 4, 33.06, -15.21, 1], "edges": [28, 30, 28, 26, 26, 34, 34, 2, 2, 0, 30, 32, 0, 32, 2, 4, 14, 12, 26, 24, 14, 16, 16, 18, 20, 18, 24, 22, 22, 20, 6, 4, 8, 6, 12, 10, 10, 8]}}, "2-5yanjin2": {"2-5yanjin2": {"type": "mesh", "hull": 15, "width": 75, "height": 118, "uvs": [1, 0.26878, 0.79733, 0.34139, 0.68645, 0.56135, 0.56885, 0.75142, 0.49797, 0.88589, 0.43781, 1, 0, 1, 0, 0.81335, 0.10853, 0.66173, 0.22949, 0.49088, 0.35381, 0.30295, 0.44789, 0.24101, 0.43109, 0.10647, 0.55205, 0, 1, 0, 0.60245, 0.3179], "triangles": [4, 8, 3, 7, 8, 4, 5, 6, 7, 4, 5, 7, 15, 11, 12, 13, 14, 0, 13, 15, 12, 13, 1, 15, 0, 1, 13, 2, 15, 1, 10, 11, 15, 9, 15, 2, 15, 9, 10, 3, 9, 2, 8, 9, 3], "vertices": [1, 5, 52.1, -12.04, 1, 1, 5, 36.36, -4.52, 1, 2, 5, 10.33, -12.61, 0.88889, 3, 84.61, -63.55, 0.11111, 2, 5, -13.09, -18.26, 0.55556, 3, 62.18, -54.73, 0.44444, 1, 3, 46.31, -49.41, 1, 1, 3, 32.85, -44.9, 1, 1, 3, 32.85, -12.07, 1, 1, 3, 54.87, -12.07, 1, 2, 5, -24.25, 16.08, 0.33333, 3, 72.76, -20.21, 0.66667, 2, 5, -2.53, 20.23, 0.66667, 3, 92.92, -29.28, 0.33333, 2, 5, 20.98, 25.32, 0.88889, 3, 115.1, -38.6, 0.11111, 1, 5, 31.01, 23.74, 1, 1, 5, 43.29, 33.89, 1, 1, 5, 58.78, 33.67, 1, 1, 5, 78.07, 6.16, 1, 1, 5, 30.24, 9.04, 1], "edges": [12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 26, 28, 24, 26, 22, 30, 30, 2, 0, 28, 2, 0, 2, 4, 4, 6, 10, 12, 10, 8, 8, 6]}}}}], "animations": {"animation": {"bones": {"bone3": {"rotate": [{"angle": 11.11, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "angle": 11.64, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -11.56, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 1, "angle": 11.11, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 1.0333, "angle": 11.64, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -11.56, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 2, "angle": 11.11, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 2.3333, "angle": 3.82, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": -3.63, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 3.82, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "angle": -3.63, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "angle": 3.82}, {"time": 4.6667, "angle": 11.11}], "translate": [{"x": -0.59, "y": 1.52, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "y": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -7.67, "y": -1.48, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "y": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": -7.67, "y": -1.48, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 1, "x": -0.59, "y": 1.52, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 1.0333, "y": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": -7.67, "y": -1.48, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "y": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": -7.67, "y": -1.48, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 2, "x": -0.59, "y": 1.52, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 2.3333, "y": 1.62, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "x": -1.22, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "x": -0.81, "y": -2.03, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "x": -1.22, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "y": 1.62, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "x": -1.22, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": -0.81, "y": -2.03, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "x": -1.22, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "y": 1.62}, {"time": 4.6667, "x": -0.59, "y": 1.52}], "scale": [{"x": 1.004, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "x": 1.007, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 0.967, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 1.007, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 0.967, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 1, "x": 1.004, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 1.0333, "x": 1.007, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": 0.967, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": 1.007, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": 0.967, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 2, "x": 1.004}]}, "bone9": {"rotate": [{"angle": 9.3, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 11.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -13.66, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 9.3, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1.0667, "angle": 11.01, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -13.66, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "angle": 9.3, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.3333, "angle": 4.28, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.4, "angle": 4.99, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": -5.23, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 3.3333, "angle": 4.28, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 3.4, "angle": 4.99, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "angle": -5.23, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4.3333, "angle": 4.28, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.6667, "angle": 9.3}]}, "bone10": {"rotate": [{"angle": 6.01, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 11.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -13.66, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 6.01, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 1.1333, "angle": 11.01, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -13.66, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": 6.01, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.3333, "angle": 2.92, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.4667, "angle": 4.99, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": -5.23, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 3.3333, "angle": 2.92, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 3.4667, "angle": 4.99, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": -5.23, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 4.3333, "angle": 2.92, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.6667, "angle": 6.01}]}, "bone11": {"rotate": [{"angle": 17.15, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 19.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -12.65, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 17.15, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1.0667, "angle": 19.37, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -12.65, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "angle": 17.15, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.3333, "angle": 4.22, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.4, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "angle": -1.63, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 3.3333, "angle": 4.22, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 3.4, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "angle": -1.63, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4.3333, "angle": 4.22, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.6667, "angle": 17.15}]}, "bone12": {"rotate": [{"angle": 12.88, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 19.37, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -12.65, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 12.88, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 1.1333, "angle": 19.37, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -12.65, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": 12.88, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.3333, "angle": 3.38, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.4667, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": -1.63, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 3.3333, "angle": 3.38, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 3.4667, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": -1.63, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 4.3333, "angle": 3.38, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.6667, "angle": 12.88}]}, "bone4": {"rotate": [{"angle": -8.61, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": -11.59, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 11.32, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": -8.61, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.1, "angle": -11.59, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 11.32, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -8.61, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.3333, "angle": -4.55, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.4333, "angle": -7.71, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 16.59, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": -4.55, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.4333, "angle": -7.71, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 16.59, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.3333, "angle": -4.55, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.6667, "angle": -8.61}]}, "bone5": {"rotate": [{"angle": -8.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": -11.28, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 12.42, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": -8.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.1, "angle": -11.28, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 12.42, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -8.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.3333, "angle": -23.05, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.4333, "angle": -27.96, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "angle": 9.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": -23.05, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.4333, "angle": -27.96, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "angle": 9.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.3333, "angle": -23.05, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.6667, "angle": -8.19}]}, "bone6": {"translate": [{"x": -4, "y": 0.39, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "x": -4.36, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 0.94, "y": 0.15, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "x": -4, "y": 0.39, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1.0667, "x": -4.36, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 0.94, "y": 0.15, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "x": -4, "y": 0.39, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.3333, "x": -2.01, "y": 5.42, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.4, "x": -2.01, "y": 6.42, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "x": -2.04, "y": -7.96, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 3.3333, "x": -2.01, "y": 5.42, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 3.4, "x": -2.01, "y": 6.42, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -2.04, "y": -7.96, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4.3333, "x": -2.01, "y": 5.42, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.6667, "x": -4, "y": 0.39}]}, "bone7": {"translate": [{"x": -2.92, "y": 1.68, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "x": -3.47, "y": 1.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 4.4, "y": 1.28, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "x": -2.92, "y": 1.68, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1.0667, "x": -3.47, "y": 1.71, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 4.4, "y": 1.28, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "x": -2.92, "y": 1.68, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.3333, "x": 6.85, "y": 1.74, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 2.4, "x": 7.13, "y": 2.46, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 5.87, "y": -0.73, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "x": 7.13, "y": 2.46, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "x": 5.87, "y": -0.73, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 3.3333, "x": 6.85, "y": 1.74, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 3.4, "x": 7.13, "y": 2.46, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": 5.87, "y": -0.73, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": 7.13, "y": 2.46, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "x": 5.87, "y": -0.73, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 4.3333, "x": 6.85, "y": 1.74, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 4.6667, "x": -2.92, "y": 1.68}]}, "bone8": {"rotate": [{"time": 2.3333, "angle": 1.33, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": -20.5, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "angle": 17.17, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 3.3333, "angle": 1.33, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "angle": -20.5, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": 17.17, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 4.3333, "angle": 1.33}], "translate": [{"time": 2.3333, "x": 0.1, "y": -0.18, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "x": -1.06, "y": 2.61, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": 1.29, "y": -2.36, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 3.3333, "x": 0.1, "y": -0.18, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "x": -1.06, "y": 2.61, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "x": 1.29, "y": -2.36, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 4.3333, "x": 0.1, "y": -0.18}], "scale": [{"x": 1.17, "y": 1.016, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.901, "y": 0.946, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 1.17, "y": 1.016, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 0.901, "y": 0.946, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.17, "y": 1.016, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 0.901, "y": 0.946, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.17, "y": 1.016, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": 0.901, "y": 0.946, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.17, "y": 1.016, "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "x": 1.034, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "x": 1.435, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.1333, "x": 1.435, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 3.3333, "x": 1.034, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 3.3667, "curve": 0.25, "c3": 0.75}, {"time": 3.6333, "x": 1.435, "curve": 0.25, "c3": 0.75}, {"time": 3.8667, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "x": 1.435, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 4.3333, "x": 1.034, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 4.6667, "x": 1.17, "y": 1.016}]}}}}}