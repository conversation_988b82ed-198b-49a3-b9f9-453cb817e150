{"skeleton": {"hash": "ptw8t/6q5yj3X2Tuh335Z2dacRk", "spine": "3.8.99", "x": -118.84, "y": -17.25, "width": 225, "height": 236, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/系列2/2-10"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "bone2", "parent": "bone", "length": 142.64, "rotation": 90, "x": -0.17, "y": 1.69}, {"name": "bone3", "parent": "bone2", "x": 175.55, "y": 21.09}, {"name": "bone4", "parent": "bone2", "x": 175.87, "y": -23.91}, {"name": "bone5", "parent": "bone2", "x": 151.32, "y": -6.79}], "slots": [{"name": "2-10<PERSON><PERSON>", "bone": "root", "attachment": "2-10<PERSON><PERSON>"}, {"name": "2-10<PERSON><PERSON>2", "bone": "root", "attachment": "2-10<PERSON><PERSON>2"}, {"name": "2-10<PERSON><PERSON>1", "bone": "root", "attachment": "2-10<PERSON><PERSON>1"}, {"name": "2-10zuiba1", "bone": "bone5", "attachment": "2-10zuiba1"}], "skins": [{"name": "default", "attachments": {"2-10shenti": {"2-10shenti": {"type": "mesh", "hull": 4, "width": 225, "height": 236, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -18.94, -106.33, 1, 1, 2, -18.94, 118.67, 1, 1, 2, 217.06, 118.67, 1, 1, 2, 217.06, -106.33, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-10yanjin1": {"2-10yanjin1": {"type": "mesh", "hull": 4, "width": 20, "height": 25, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -12.49, -9.42, 1, 1, 3, -12.49, 10.58, 1, 1, 3, 12.51, 10.58, 1, 1, 3, 12.51, -9.42, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-10yanjin2": {"2-10yanjin2": {"type": "mesh", "hull": 4, "width": 18, "height": 21, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 4, -10.81, -9.43, 1, 1, 4, -10.81, 8.57, 1, 1, 4, 10.19, 8.57, 1, 1, 4, 10.19, -9.43, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-10zuiba1": {"2-10zuiba1": {"type": "mesh", "hull": 4, "width": 20, "height": 16, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-8.26, -9.54, -8.26, 10.46, 7.74, 10.46, 7.74, -9.54], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}, "2-10zuiba2": {"type": "mesh", "hull": 4, "width": 19, "height": 11, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-5.26, -9.54, -5.26, 9.46, 5.74, 9.46, 5.74, -9.54], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}, "2-10zuiba3": {"type": "mesh", "hull": 4, "width": 25, "height": 11, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-6.26, -12.54, -6.26, 12.46, 4.74, 12.46, 4.74, -12.54], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"slots": {"2-10zuiba1": {"attachment": [{"time": 0.1333, "name": "2-10zuiba2"}, {"time": 0.2667, "name": "2-10zuiba3"}, {"time": 0.4, "name": "2-10zuiba1"}, {"time": 0.5333, "name": "2-10zuiba2"}, {"time": 0.6667, "name": "2-10zuiba3"}, {"time": 0.8, "name": "2-10zuiba1"}, {"time": 0.9333, "name": "2-10zuiba2"}]}}, "bones": {"bone3": {"scale": [{}, {"time": 0.1333, "x": 0.122, "curve": "stepped"}, {"time": 0.2, "x": 0.122}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5}, {"time": 0.6333, "x": 0.122, "curve": "stepped"}, {"time": 0.7, "x": 0.122}, {"time": 0.8333}]}, "bone4": {"scale": [{"time": 0.1667}, {"time": 0.3, "x": 0.122, "curve": "stepped"}, {"time": 0.3667, "x": 0.122}, {"time": 0.5, "curve": "stepped"}, {"time": 0.6667}, {"time": 0.8, "x": 0.122, "curve": "stepped"}, {"time": 0.8667, "x": 0.122}, {"time": 1}]}}}}}