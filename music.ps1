# 极限32kbps压缩 - 高级选项
param(
    [string]$SourcePath = "F:\little\cocos_game\project_restore",
    [string]$OutputFormat = "mp3",
    [switch]$DeleteOriginal = $true,
    [switch]$VoiceOptimized = $false
)

$ffmpegPath = "C:\Users\<USER>\Desktop\ffmpeg\bin\ffmpeg.exe"

# 检查FFmpeg是否存在
if (!(Test-Path $ffmpegPath)) {
    Write-Error "FFmpeg未找到，请先安装FFmpeg"
    exit 1
}

Write-Host "=== WAV到32kbps极限压缩工具 ===" -ForegroundColor Cyan
Write-Host "源目录: $SourcePath" -ForegroundColor Yellow
Write-Host "输出格式: $OutputFormat" -ForegroundColor Yellow
Write-Host ""

# 不同格式的压缩参数
$compressionSettings = @{
    "mp3" = @{
        codec = "libmp3lame"
        ext = "mp3"
        params = @("-b:a", "32k", "-ac", "1", "-ar", "22050")
    }
    "mp3_voice" = @{
        codec = "libmp3lame" 
        ext = "mp3"
        params = @("-b:a", "32k", "-ac", "1", "-ar", "16000", "-compression_level", "0", "-lowpass", "4000")
    }
    "aac" = @{
        codec = "aac"
        ext = "aac"
        params = @("-b:a", "32k", "-ac", "1", "-ar", "22050", "-profile:a", "aac_he")
    }
    "opus" = @{
        codec = "libopus"
        ext = "opus"
        params = @("-b:a", "32k", "-ac", "1", "-ar", "16000", "-compression_level", "10")
    }
    "amr" = @{
        codec = "libopencore_amr_nb"
        ext = "amr"
        params = @("-b:a", "12.2k", "-ac", "1", "-ar", "8000")  # AMR-NB for speech
    }
}

# 选择压缩设置
$settingKey = if ($VoiceOptimized) { "mp3_voice" } else { $OutputFormat }
$settings = $compressionSettings[$settingKey]

if (!$settings) {
    Write-Error "不支持的格式: $OutputFormat"
    Write-Host "支持的格式: $($compressionSettings.Keys -join ', ')"
    exit 1
}

# 获取所有WAV文件
$wavFiles = Get-ChildItem -Path $SourcePath -Filter "*.wav" -Recurse
if ($wavFiles.Count -eq 0) {
    Write-Warning "未找到WAV文件"
    exit 0
}

Write-Host "找到 $($wavFiles.Count) 个WAV文件" -ForegroundColor Green
Write-Host ""

$totalOriginalSize = 0
$totalCompressedSize = 0
$successCount = 0

foreach ($file in $wavFiles) {
    $outputPath = $file.FullName -replace '\.wav$', "_32k.$($settings.ext)"
    
    Write-Host "处理: $($file.Name)" -NoNewline
    
    # 构建FFmpeg命令
    $ffmpegArgs = @("-i", $file.FullName, "-codec:a", $settings.codec) + $settings.params + @($outputPath, "-y")
    
    try {
        # 执行压缩
        $process = Start-Process -FilePath $ffmpegPath -ArgumentList $ffmpegArgs -NoNewWindow -Wait -PassThru
        
        if ($process.ExitCode -eq 0 -and (Test-Path $outputPath)) {
            $originalSize = $file.Length
            $compressedSize = (Get-Item $outputPath).Length
            $totalOriginalSize += $originalSize
            $totalCompressedSize += $compressedSize
            $successCount++
            
            $reduction = [math]::Round((1 - $compressedSize/$originalSize) * 100, 1)
            Write-Host " ✓ $([math]::Round($originalSize/1KB, 0))KB -> $([math]::Round($compressedSize/1KB, 0))KB (-$reduction%)" -ForegroundColor Green
            
            # 删除原文件（如果指定）
            if ($DeleteOriginal) {
                Remove-Item $file.FullName -Force
                Write-Host "    已删除原文件" -ForegroundColor Yellow
            Remove-Item $file.FullName -Force
            Rename-Item $outputPath $finalPath
            Write-Host "    已替换原文件" -ForegroundColor Yellow
            }
        } else {
            Write-Host " ✗ 压缩失败" -ForegroundColor Red
        }
    }
    catch {
        Write-Host " ✗ 错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 显示统计信息
Write-Host ""
Write-Host "=== 压缩统计 ===" -ForegroundColor Cyan
Write-Host "成功处理: $successCount / $($wavFiles.Count) 个文件"
Write-Host "原始总大小: $([math]::Round($totalOriginalSize/1MB, 2)) MB"
Write-Host "压缩后总大小: $([math]::Round($totalCompressedSize/1MB, 2)) MB"
if ($totalOriginalSize -gt 0) {
    $totalReduction = [math]::Round((1 - $totalCompressedSize/$totalOriginalSize) * 100, 1)
    Write-Host "总体积减少: $totalReduction%" -ForegroundColor Green
    Write-Host "节省空间: $([math]::Round(($totalOriginalSize - $totalCompressedSize)/1MB, 2)) MB"
}

Write-Host ""
Write-Host "注意: 32kbps压缩会显著降低音质，建议仅用于:" -ForegroundColor Yellow
Write-Host "- 语音录音" 
Write-Host "- 对音质要求不高的场景"
Write-Host "- 需要极小文件体积的情况"