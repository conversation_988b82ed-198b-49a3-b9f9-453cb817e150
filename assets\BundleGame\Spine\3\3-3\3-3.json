{"skeleton": {"hash": "h6JsmZojuWYkqXeS90rCX1kZRLA", "spine": "3.8.99", "x": -60.17, "y": -2.04, "width": 122.06, "height": 228.68, "images": "./images/", "audio": "E:/外包/2.15音乐游戏/系列3/3-3"}, "bones": [{"name": "root", "scaleX": 0.7353, "scaleY": 0.7353}, {"name": "bone", "parent": "root"}, {"name": "bone2", "parent": "bone", "length": 101.4, "rotation": 90, "x": -0.64, "y": 0.16}, {"name": "bone2b", "parent": "bone2", "length": 101.4, "x": 101.4}, {"name": "bone2b2", "parent": "bone2b", "x": 140.63, "y": 0.26}, {"name": "bone2b3", "parent": "bone2b2", "length": 50.98, "rotation": 8.51, "x": 0.69, "y": 3.09}, {"name": "bone2b4", "parent": "bone2b", "x": 84.04, "y": -0.08}, {"name": "bone2b5", "parent": "bone2b", "x": 112.16, "y": -0.08}], "slots": [{"name": "3-3<PERSON><PERSON>", "bone": "root", "attachment": "3-3<PERSON><PERSON>"}, {"name": "3-3<PERSON><PERSON>", "bone": "root", "attachment": "3-3<PERSON><PERSON>"}, {"name": "3-3yanjin2", "bone": "root", "attachment": "3-3yanjin2"}, {"name": "3-3yanjin1", "bone": "root", "attachment": "3-3yanjin1"}, {"name": "3-3<PERSON><PERSON><PERSON>", "bone": "root", "attachment": "3-3<PERSON><PERSON><PERSON>"}, {"name": "3-3maozi2", "bone": "root", "attachment": "3-3maozi2"}, {"name": "3-3maozi1", "bone": "root", "attachment": "3-3maozi1"}], "skins": [{"name": "default", "attachments": {"3-3maozi1": {"3-3maozi1": {"type": "mesh", "hull": 4, "width": 29, "height": 68, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 5, -3.83, -7.68, 1, 1, 5, 0.46, 21, 1, 1, 5, 67.71, 10.94, 1, 1, 5, 63.42, -17.74, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "3-3maozi2": {"3-3maozi2": {"type": "mesh", "hull": 4, "width": 138, "height": 49, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 4, -19.97, -65.07, 1, 1, 4, -19.97, 72.93, 1, 1, 4, 29.03, 72.93, 1, 1, 4, 29.03, -65.07, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "3-3meimao": {"3-3meimao": {"type": "mesh", "hull": 4, "width": 74, "height": 11, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 7, -2.5, -38.73, 1, 1, 7, -2.5, 35.27, 1, 1, 7, 8.5, 35.27, 1, 1, 7, 8.5, -38.73, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "3-3shenti": {"3-3shenti": {"type": "mesh", "hull": 4, "width": 134, "height": 182, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -2.93, -65.81, 1, 1, 2, -2.93, 68.19, 1, 1, 2, 179.07, 68.19, 1, 1, 2, 179.07, -65.81, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "3-3tou": {"3-3tou": {"type": "mesh", "hull": 4, "width": 166, "height": 123, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, 23.66, -84.81, 1, 1, 3, 23.66, 81.19, 1, 1, 3, 146.66, 81.19, 1, 1, 3, 146.66, -84.81, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "3-3yanjin1": {"3-3yanjin1": {"type": "mesh", "hull": 4, "width": 20, "height": 19, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 6, -7.37, 16.27, 1, 1, 6, -7.37, 36.27, 1, 1, 6, 11.63, 36.27, 1, 1, 6, 11.63, 16.27, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "3-3yanjin2": {"3-3yanjin2": {"type": "mesh", "hull": 4, "width": 20, "height": 19, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 6, -7.37, -31.73, 1, 1, 6, -7.37, -11.73, 1, 1, 6, 11.63, -11.73, 1, 1, 6, 11.63, -31.73, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"bones": {"bone2": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.977, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "bone2b": {"rotate": [{"angle": 2.82, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 2.24, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 6.72, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.5, "angle": 2.82}], "translate": [{"x": -0.76, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -5.86, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.5, "x": -0.76}]}, "bone2b2": {"rotate": [{"angle": -13.45, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": -5.73, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 14.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -14.61, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5, "angle": -13.45}]}, "bone2b4": {"translate": [{"x": 0.84, "y": 0.06, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "x": 3.15, "y": -0.12, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -3.14, "y": 0.37, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5, "x": 0.84, "y": 0.06}]}, "bone2b5": {"translate": [{"x": -2.97, "y": 0.43, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "x": 2.7, "y": -0.11, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -6.27, "y": 0.74, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5, "x": -2.97, "y": 0.43}]}}}}}