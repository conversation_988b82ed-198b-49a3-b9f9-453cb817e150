var $9AdInfodefault = require("AdInfo").default;
require("GameCommon_Jy");
var $9EventManager = require("EventManager");
var $9EventType = require("EventType");
var $9AudioManager = require("AudioManager");
var $9BundlePath = require("BundlePath");
var $9GameUtils = require("GameUtils");
var $9Utils = require("Utils");
cc.Class({
  extends: cc.Component,
  properties: {
    roleParent_1: cc.Node,
    triggerParent: cc.Node,
    btns_2: cc.Node,
    btns_1: cc.Node,
    transNode: cc.Node,
    currentSceneIndex: 1,
    triggerIndex: 0,
    audioIndexArr: [],
    musicRoleList: []
  },
  onLoad: function () {
    this.audioIndexArr = new Array();
    for (var t = 0; t < this.roleParent_1.childrenCount; t++) {
      var i = this.roleParent_1.children[t].getComponent("MusicRole_3");
      if (i) {
        this.musicRoleList.push(i);
        $9GameUtils.showAnimation(i.node, t);
      }
    }
  },
  onEnable: function () {
    $9EventManager.on($9EventType.JUDGEISCANBIANSHEN_4, this.judgeIsCanBianShen, this);
    $9EventManager.on($9EventType.BIANSHEN_4, this.roleBianShen, this);
    $9EventManager.on($9EventType.RESETALLIDELIMG_4, this.resetAllIdle, this);
    $9EventManager.on($9EventType.SHOWBTN_1_4, this.showBtns_1, this);
    $9EventManager.on($9EventType.SHOWBTN_2_4, this.showBtns_2, this);
    $9EventManager.on($9EventType.STOPAUDIO_4, this.stopSound, this);
    $9EventManager.on($9EventType.OPENAUDIO, this.openAudio, this);
    $9EventManager.on($9EventType.CLEARARR, this.clear, this);
    this.initData();
    $9GameUtils.isInScene = true;
  },
  onDisable: function () {
    $9EventManager.off($9EventType.JUDGEISCANBIANSHEN_4, this.judgeIsCanBianShen, this);
    $9EventManager.off($9EventType.BIANSHEN_4, this.roleBianShen, this);
    $9EventManager.off($9EventType.RESETALLIDELIMG_4, this.resetAllIdle, this);
    $9EventManager.off($9EventType.SHOWBTN_1_4, this.showBtns_1, this);
    $9EventManager.off($9EventType.SHOWBTN_2_4, this.showBtns_2, this);
    $9EventManager.off($9EventType.STOPAUDIO_4, this.stopSound, this);
    $9EventManager.off($9EventType.OPENAUDIO, this.openAudio, this);
    $9EventManager.off($9EventType.CLEARARR, this.clear, this);
  },
  start: function () {},
  initData: function () {
    this.triggerIndex = 0;
    this.needBianShenNode = null;
    this.changeRoleParent();
    this.transNode.active = false;
    this.transNode.opacity = 0;
  },
  changeRoleParent: function () {
    this.roleParent_1.active = true;
    this.showRoleParent(0, 0, true);
    this.showBtns_2(-1, false);
    this.showBtns_1(-1);
  },
  showRoleParent: function (t, i, e) {
    if (e) {
      for (var n = 0; n < this.musicRoleList.length; n++) {
        var s = this.musicRoleList[n];
        if (s) {
          s.changeRoleState(0);
          s.initData(n);
        }
      }
    } else {
      for (n = 0; n < this.musicRoleList.length; n++) {
        if (t == n) {
          var o = this.musicRoleList[n];
          o && o.changeRoleState(i);
          break;
        }
      }
    }
  },
  showBtns_2: function (t, i) {
    if (-1 == t) {
      for (var e = 0; e < this.btns_2.childrenCount; e++) {
        this.btns_2.children[e].active = false;
      }
    } else {
      for (var n = 0; n < this.btns_2.childrenCount; n++) {
        if (t == n) {
          this.btns_2.children[n].active = !i;
          break;
        }
      }
    }
  },
  showBtns_1: function (t) {
    if (-1 == t) {
      for (var i = 0; i < this.btns_1.childrenCount; i++) {
        this.btns_1.children[i].active = true;
        this.btns_1.children[i].children[0] && (this.btns_1.children[i].children[0].getComponent(cc.Sprite).spriteFrame = this.btns_1.children[i].getComponent(cc.Sprite).spriteFrame);
      }
    } else {
      for (var e = 0; e < this.btns_1.childrenCount; e++) {
        if (t == e) {
          this.btns_1.children[e].active = true;
          break;
        }
      }
    }
  },
  judgeIsCanBianShen: function (t) {
    for (var i = this.musicRoleList.length - 1; i >= 0; i--) {
      var e = this.musicRoleList[i];
      if ($9Utils.cheakCollierBox(t, this.triggerParent.children[i]) && !e.getIsCanClick()) {
        $9GameUtils.isCanInstall = true;
        this.triggerIndex = $9Utils.getTouchNum(t.name);
        this.needBianShenNode = this.musicRoleList[i].node;
        e.changeRoleColor(true);
        this.resetAllIdle(i);
        break;
      }
      $9GameUtils.isCanInstall = false;
      this.triggerIndex = 0;
    }
  },
  resetAllIdle: function (t) {
    for (var i = 0; i < this.musicRoleList.length; i++) {
      t != i && this.musicRoleList[i].changeRoleColor(false);
    }
  },
  roleBianShen: function () {
    if (0 != this.triggerIndex && $9GameUtils.isCanInstall) {
      var t = this.needBianShenNode.getComponent("MusicRole_3");
      if (t) {
        t.changeRoleState(this.triggerIndex);
        t.setClickState(this.triggerIndex);
        this.playSound();
        this.audioIndexArr.push(this.triggerIndex);
      }
      this.showBtns_2(this.triggerIndex - 1, false);
      this.triggerIndex = 0;
      this.needBianShenNode = null;
    }
  },
  openAudio: function () {
    if (!(this.audioIndexArr.length <= 0)) {
      for (var t = 0; t < this.audioIndexArr.length; t++) {
        $9AudioManager.playAudio($9BundlePath.Level3, "Sound/role_2_" + this.audioIndexArr[t], true);
      }
    }
  },
  playSound: function () {
    $9AudioManager.playAudio($9BundlePath.Level3, "Sound/role_2_" + this.triggerIndex, true);
  },
  stopSound: function (t) {
    $9AudioManager.stopOneAudio("Sound/role_2_" + t);
    for (var i = 0; i < this.audioIndexArr.length; i++) {
      if (this.audioIndexArr[i] == t) {
        this.audioIndexArr.splice(i, 1);
        break;
      }
    }
  },
  rePlay: function () {
    var t = this;
    var i = this;
    $9AdInfodefault.adsManager.showInterstitial();
    this.transScene(function () {
      i.triggerIndex = 0;
      i.needBianShenNode = null;
      $9AudioManager.stopAllAudio();
      i.changeRoleParent();
      t.clear();
    }, function () {
      t.transNode.active = false;
    }, 0);
  },
  transScene: function (t, i, e) {
    this.transNode.opacity = 0;
    this.transNode.active = true;
    cc.tween(this.transNode).delay(e).to(.5, {
      opacity: 255
    }).call(function () {
      t && t();
    }).to(1, {
      opacity: 0
    }).call(function () {
      i && i();
    }).start();
  },
  clear: function () {
    this.audioIndexArr.length = 0;
  },
  backHome: function () {
    var t = this;
    $9GameUtils.back(function () {
      t.node.destroy();
      t.clear();
      $9AudioManager.stopAllAudio();
      $9GameUtils.isInGame = false;
      "vivo" != globalThis.channel && "oppo" != globalThis.channel || globalThis.AdInfo.adsManager.showInterstitialJudgmentArea();
      $9GameUtils.curscene = 1;
    });
  }
});