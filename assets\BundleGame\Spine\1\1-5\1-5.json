{"skeleton": {"hash": "YLKN/Ojfi/VMdT+bRsDRSbktkcc", "spine": "3.8.99", "x": -71.52, "y": -2.23, "width": 144.77, "height": 213.39, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/1-5"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "scaleX": 0.8368, "scaleY": 0.8368}, {"name": "bone2", "parent": "bone", "length": 127.01, "rotation": 90, "x": -0.82, "y": 0.05}, {"name": "bone3", "parent": "bone2", "length": 84.59, "x": 126.93, "y": 0.02}, {"name": "bone4", "parent": "bone3", "length": 34.91, "rotation": 40.12, "x": 75.58, "y": 41.99}, {"name": "bone5", "parent": "bone3", "length": 31.87, "rotation": -35.04, "x": 80.38, "y": -45.59}, {"name": "bone6", "parent": "bone3", "x": 49.83, "y": -0.17}, {"name": "bone7", "parent": "bone3", "x": 71.28, "y": -0.14}, {"name": "bone8", "parent": "bone3", "x": 5.71, "y": -8.86}, {"name": "bone9", "parent": "bone3", "length": 26.56, "rotation": 125.54, "x": 30.59, "y": 23.11}, {"name": "bone10", "parent": "bone9", "length": 30.31, "rotation": 9.46, "x": 26.56}, {"name": "bone11", "parent": "bone3", "length": 25.21, "rotation": -126.21, "x": 30.96, "y": -21.03}, {"name": "bone12", "parent": "bone11", "length": 34.48, "rotation": -12.21, "x": 25.21}], "slots": [{"name": "1-5<PERSON><PERSON><PERSON>", "bone": "bone", "attachment": "1-5<PERSON><PERSON><PERSON>"}, {"name": "1-5<PERSON><PERSON>", "bone": "bone", "attachment": "1-5<PERSON><PERSON>"}, {"name": "1-5<PERSON><PERSON>", "bone": "bone", "attachment": "1-5<PERSON><PERSON>"}, {"name": "1-5zu<PERSON>", "bone": "bone", "attachment": "1-5zu<PERSON>"}, {"name": "1-5<PERSON><PERSON>", "bone": "bone", "attachment": "1-5<PERSON><PERSON>"}, {"name": "1-5<PERSON><PERSON><PERSON>", "bone": "bone", "attachment": "1-5<PERSON><PERSON><PERSON>"}], "skins": [{"name": "default", "attachments": {"1-5meimao": {"1-5meimao": {"type": "mesh", "hull": 4, "width": 88, "height": 11, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 7, -4.92, -46.23, 1, 1, 7, -4.92, 41.77, 1, 1, 7, 6.08, 41.77, 1, 1, 7, 6.08, -46.23, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-5shenti": {"1-5shenti": {"type": "mesh", "hull": 4, "width": 115, "height": 154, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -2.71, -59.36, 1, 1, 2, -2.71, 55.64, 1, 1, 2, 151.29, 55.64, 1, 1, 2, 151.29, -59.36, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-5tou": {"1-5tou": {"type": "mesh", "hull": 26, "width": 154, "height": 143, "uvs": [0.75199, 0.18741, 0.76978, 0.1133, 0.82116, 0.07449, 0.90791, 0.09533, 0.98132, 0.15857, 1, 0.23691, 0.96263, 0.33824, 0.88806, 0.40388, 0.92978, 0.50549, 0.92084, 0.6991, 0.80761, 0.87345, 0.59705, 1, 0.41658, 1, 0.26363, 0.92115, 0.10372, 0.78737, 0.05902, 0.57211, 0.09883, 0.41385, 0, 0.32634, 0, 0.23074, 0.14185, 0.07484, 0.22516, 0.07705, 0.25862, 0.17265, 0.31031, 0.13487, 0.2815, 0.06107, 0.3987, 0, 0.65465, 0, 0.148, 0.32119, 0.1999, 0.23957, 0.83244, 0.30934, 0.78079, 0.22484], "triangles": [6, 28, 5, 28, 4, 5, 4, 29, 3, 3, 29, 2, 29, 1, 2, 17, 18, 26, 18, 19, 27, 21, 19, 20, 7, 28, 6, 4, 28, 29, 0, 1, 29, 16, 17, 26, 26, 18, 27, 21, 27, 19, 10, 7, 9, 0, 27, 22, 26, 28, 13, 22, 27, 21, 16, 13, 15, 10, 11, 28, 13, 28, 11, 13, 11, 12, 0, 22, 25, 22, 24, 25, 14, 15, 13, 22, 23, 24, 13, 16, 26, 26, 27, 29, 0, 29, 27, 10, 28, 7, 29, 28, 26, 9, 7, 8], "vertices": [1, 3, 98.56, -40.18, 1, 2, 3, 109.16, -42.92, 0.34391, 5, 22.03, 18.7, 0.65609, 2, 3, 114.71, -50.83, 0.01058, 5, 31.11, 15.41, 0.98942, 2, 3, 111.72, -64.19, 0.00155, 5, 36.34, 2.76, 0.99845, 1, 5, 35.43, -11.69, 1, 1, 5, 27.91, -20.47, 1, 2, 3, 76.99, -72.62, 0.33333, 5, 12.74, -24.08, 0.66667, 1, 3, 67.6, -61.14, 1, 1, 3, 53.07, -67.56, 1, 1, 3, 25.39, -66.18, 1, 1, 3, 0.45, -48.75, 1, 1, 3, -17.64, -16.32, 1, 1, 3, -17.64, 11.47, 1, 1, 3, -6.37, 35.03, 1, 1, 3, 12.76, 59.65, 1, 1, 3, 43.55, 66.54, 1, 1, 3, 66.18, 60.4, 1, 2, 3, 78.69, 75.62, 0.33333, 4, 24.05, 23.72, 0.66667, 2, 3, 92.36, 75.62, 0.00202, 4, 34.5, 14.91, 0.99798, 2, 3, 114.65, 53.78, 0.01307, 4, 37.47, -16.16, 0.98693, 2, 3, 114.34, 40.95, 0.34641, 4, 28.96, -25.77, 0.65359, 1, 3, 100.67, 35.8, 1, 1, 3, 106.07, 27.84, 1, 1, 3, 116.62, 32.27, 1, 1, 3, 125.36, 14.22, 1, 1, 3, 125.36, -25.19, 1, 1, 3, 79.43, 52.83, 1, 1, 3, 91.1, 44.84, 1, 1, 3, 81.12, -52.57, 1, 1, 3, 93.2, -44.62, 1], "edges": [32, 52, 52, 54, 54, 42, 42, 40, 40, 38, 38, 36, 34, 36, 34, 32, 42, 44, 44, 46, 46, 48, 32, 30, 30, 28, 28, 26, 26, 24, 22, 24, 22, 20, 20, 18, 18, 16, 16, 14, 14, 56, 56, 58, 58, 0, 48, 50, 0, 50, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 14, 12, 12, 10]}}, "1-5toufa": {"1-5toufa": {"type": "mesh", "hull": 19, "width": 173, "height": 101, "uvs": [1, 0.34487, 0.95661, 0.66042, 0.8325, 0.88625, 0.63162, 0.93884, 0.57547, 0.62479, 0.57284, 0.33029, 0.42583, 0.40174, 0.41793, 0.7278, 0.38021, 1, 0.1995, 1, 0.072, 0.79362, 0.02024, 0.57124, 0, 0.30678, 0, 0.08444, 0.12353, 0.06941, 0.26828, 0.0619, 0.7101, 0, 0.8645, 0, 1, 0, 0.3381, 0.21992, 0.6873, 0.18559], "triangles": [3, 4, 2, 2, 4, 1, 1, 20, 0, 17, 18, 0, 8, 9, 7, 11, 6, 7, 7, 9, 10, 11, 7, 10, 19, 11, 12, 12, 13, 14, 17, 20, 16, 4, 5, 20, 14, 15, 19, 16, 19, 15, 6, 19, 5, 6, 11, 19, 20, 5, 16, 16, 5, 19, 14, 19, 12, 1, 4, 20, 17, 0, 20], "vertices": [2, 11, 64.04, 26.52, 0.12703, 12, 32.34, 34.13, 0.87297, 2, 11, 76.81, -3.63, 0.05752, 12, 51.2, 7.37, 0.94248, 3, 3, -40.15, -59.4, 0.00044, 11, 72.96, -34.71, 0.07692, 12, 54.01, -23.83, 0.92264, 3, 3, -45.47, -24.64, 0.11199, 11, 48.06, -59.53, 0.11997, 12, 34.92, -53.35, 0.76804, 3, 3, -13.75, -14.93, 0.44532, 11, 21.49, -39.67, 0.10516, 12, 4.75, -39.57, 0.44952, 1, 3, 16, -14.48, 1, 1, 3, 8.78, 10.96, 1, 3, 3, -24.15, 12.32, 0.46762, 9, 23.05, 50.82, 0.03419, 10, 4.88, 50.7, 0.49819, 3, 3, -51.64, 18.85, 0.1381, 9, 44.34, 69.39, 0.03698, 10, 28.94, 65.53, 0.82492, 3, 3, -51.64, 50.11, 0.01683, 9, 69.77, 51.22, 0.02001, 10, 51.05, 43.42, 0.96316, 3, 3, -30.8, 72.17, 0.0048, 9, 75.61, 21.44, 0.00768, 10, 51.9, 13.09, 0.98752, 3, 3, -8.34, 81.12, 0.00099, 9, 69.84, -2.04, 0.02586, 10, 42.35, -9.13, 0.97316, 3, 3, 18.37, 84.62, 2e-05, 9, 57.16, -25.81, 0.08934, 10, 25.94, -30.49, 0.91064, 3, 3, 40.83, 84.62, 0.11111, 9, 44.11, -44.09, 0.1433, 10, 10.06, -46.37, 0.74559, 3, 3, 42.35, 63.25, 0.44444, 9, 25.84, -32.9, 0.12233, 10, -6.12, -32.33, 0.43322, 1, 3, 43.11, 38.21, 1, 1, 3, 49.36, -38.22, 1, 3, 3, 49.36, -64.93, 0.44444, 11, 24.55, 40.78, 0.16252, 12, -9.27, 39.72, 0.39303, 3, 3, 49.36, -88.38, 0.11111, 11, 43.47, 54.63, 0.19684, 12, 6.29, 57.25, 0.69205, 1, 3, 27.15, 26.13, 1, 1, 3, 30.61, -34.28, 1], "edges": [30, 28, 28, 26, 24, 26, 24, 22, 22, 20, 20, 18, 16, 18, 16, 14, 14, 12, 12, 38, 38, 30, 10, 8, 8, 6, 6, 4, 4, 2, 0, 36, 2, 0, 34, 36, 32, 34, 32, 40, 40, 10, 10, 12, 30, 32]}}, "1-5yanjin": {"1-5yanjin": {"type": "mesh", "hull": 4, "width": 86, "height": 32, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 6, -14.48, -43.2, 1, 1, 6, -14.48, 42.8, 1, 1, 6, 17.52, 42.8, 1, 1, 6, 17.52, -43.2, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-5zuiba": {"1-5zuiba": {"type": "mesh", "hull": 12, "width": 33, "height": 19, "uvs": [1, 0.36495, 0.91588, 0.65174, 0.69572, 0.86306, 0.44079, 0.94105, 0.18007, 0.92595, 0, 0.77501, 0, 0.52092, 0.23222, 0.4857, 0.46252, 0.38256, 0.62764, 0.19639, 0.75076, 0, 1, 0], "triangles": [10, 11, 0, 0, 9, 10, 1, 9, 0, 5, 6, 7, 2, 9, 1, 8, 9, 2, 4, 5, 7, 3, 7, 8, 3, 8, 2, 4, 7, 3], "vertices": [1, 8, 3.71, -13.51, 1, 1, 8, -1.74, -10.74, 1, 1, 8, -5.75, -3.47, 1, 1, 8, -7.23, 4.94, 1, 1, 8, -6.95, 13.55, 1, 1, 8, -4.08, 19.49, 1, 1, 8, 0.75, 19.49, 1, 1, 8, 1.42, 11.82, 1, 1, 8, 3.38, 4.22, 1, 1, 8, 6.92, -1.22, 1, 1, 8, 10.65, -5.29, 1, 1, 8, 10.65, -13.51, 1], "edges": [12, 14, 14, 16, 16, 18, 20, 22, 18, 20, 0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 10, 12, 8, 10]}}}}], "animations": {"animation": {"bones": {"bone3": {"rotate": [{"angle": 11.11, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "angle": 11.64, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -11.56, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 1, "angle": 11.11, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 1.0333, "angle": 11.64, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -11.56, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 2, "angle": 11.11, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 2.1667, "angle": 3.82, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -3.63, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 3.82, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -3.63, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "angle": 3.82}, {"time": 4.3333, "angle": 11.11}], "translate": [{"x": -0.59, "y": 1.52, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "y": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -7.67, "y": -1.48, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "y": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": -7.67, "y": -1.48, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 1, "x": -0.59, "y": 1.52, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 1.0333, "y": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": -7.67, "y": -1.48, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "y": 1.77, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": -7.67, "y": -1.48, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 2, "x": -0.59, "y": 1.52, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 2.1667, "y": 1.62, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "x": -1.22, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -0.81, "y": -2.03, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "x": -1.22, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "y": 1.62, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "x": -1.22, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -0.81, "y": -2.03, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "x": -1.22, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "y": 1.62}, {"time": 4.3333, "x": -0.59, "y": 1.52}], "scale": [{"x": 1.004, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0333, "x": 1.007, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 0.967, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 1.007, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 0.967, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 1, "x": 1.004, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 1.0333, "x": 1.007, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": 0.967, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "x": 1.007, "curve": 0.25, "c3": 0.75}, {"time": 1.8, "x": 0.967, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 2, "x": 1.004}]}, "bone9": {"rotate": [{"angle": 9.3, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 11.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -13.66, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 9.3, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1.0667, "angle": 11.01, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -13.66, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "angle": 9.3, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.1667, "angle": 4.28, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.2333, "angle": 4.99, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": -5.23, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 3.1667, "angle": 4.28, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 3.2333, "angle": 4.99, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -5.23, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4.1667, "angle": 4.28, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.3333, "angle": 9.3}]}, "bone10": {"rotate": [{"angle": 6.01, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 11.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -13.66, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 6.01, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 1.1333, "angle": 11.01, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -13.66, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": 6.01, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.1667, "angle": 2.92, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.3, "angle": 4.99, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": -5.23, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 3.1667, "angle": 2.92, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 3.3, "angle": 4.99, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": -5.23, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 4.1667, "angle": 2.92, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.3333, "angle": 6.01}]}, "bone11": {"rotate": [{"angle": 17.15, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 19.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -12.65, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 17.15, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1.0667, "angle": 19.37, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -12.65, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "angle": 17.15, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.1667, "angle": 4.22, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.2333, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": -1.63, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 3.1667, "angle": 4.22, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 3.2333, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "angle": -1.63, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4.1667, "angle": 4.22, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.3333, "angle": 17.15}]}, "bone12": {"rotate": [{"angle": 12.88, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 19.37, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -12.65, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 12.88, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 1.1333, "angle": 19.37, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -12.65, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 2, "angle": 12.88, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.1667, "angle": 3.38, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 2.3, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": -1.63, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 3.1667, "angle": 3.38, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 3.3, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 3.8, "angle": -1.63, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 4.1667, "angle": 3.38, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 4.3333, "angle": 12.88}]}, "bone4": {"rotate": [{"angle": -8.61, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": -11.59, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 11.32, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": -8.61, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.1, "angle": -11.59, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 11.32, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -8.61, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.1667, "angle": -4.55, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.2667, "angle": -7.71, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": 16.59, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.1667, "angle": -4.55, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.2667, "angle": -7.71, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "angle": 16.59, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.1667, "angle": -4.55, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.3333, "angle": -8.61}]}, "bone5": {"rotate": [{"angle": -8.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": -11.28, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 12.42, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": -8.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.1, "angle": -11.28, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 12.42, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -8.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.1667, "angle": -23.05, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 2.2667, "angle": -27.96, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": 9.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.1667, "angle": -23.05, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 3.2667, "angle": -27.96, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "angle": 9.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 4.1667, "angle": -23.05, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 4.3333, "angle": -8.19}]}, "bone6": {"translate": [{"x": -4, "y": 0.39, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "x": -4.36, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 0.94, "y": 0.15, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "x": -4, "y": 0.39, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1.0667, "x": -4.36, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 0.94, "y": 0.15, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "x": -4, "y": 0.39, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.1667, "x": -2.01, "y": 5.42, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.2333, "x": -2.01, "y": 6.42, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": -2.04, "y": -7.96, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 3.1667, "x": -2.01, "y": 5.42, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 3.2333, "x": -2.01, "y": 6.42, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": -2.04, "y": -7.96, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 4.1667, "x": -2.01, "y": 5.42, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 4.3333, "x": -4, "y": 0.39}]}, "bone7": {"translate": [{"x": -2.92, "y": 1.68, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "x": -3.47, "y": 1.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 4.4, "y": 1.28, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "x": -2.92, "y": 1.68, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1.0667, "x": -3.47, "y": 1.71, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 4.4, "y": 1.28, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "x": -2.92, "y": 1.68, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 2.1667, "x": 6.85, "y": 1.74, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 2.2333, "x": 7.13, "y": 2.46, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": 5.87, "y": -0.73, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": 7.13, "y": 2.46, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 5.87, "y": -0.73, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 3.1667, "x": 6.85, "y": 1.74, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 3.2333, "x": 7.13, "y": 2.46, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": 5.87, "y": -0.73, "curve": 0.25, "c3": 0.75}, {"time": 3.7333, "x": 7.13, "y": 2.46, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": 5.87, "y": -0.73, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 4.1667, "x": 6.85, "y": 1.74, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 4.3333, "x": -2.92, "y": 1.68}]}, "bone8": {"rotate": [{"time": 2.1667, "angle": 1.33, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "angle": -20.5, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "angle": 17.17, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 3.1667, "angle": 1.33, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "angle": -20.5, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "angle": 17.17, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 4.1667, "angle": 1.33}], "translate": [{"time": 2.1667, "x": 0.1, "y": -0.18, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": -1.06, "y": 2.61, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "x": 1.29, "y": -2.36, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 3.1667, "x": 0.1, "y": -0.18, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "x": -1.06, "y": 2.61, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 1.29, "y": -2.36, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 4.1667, "x": 0.1, "y": -0.18}], "scale": [{"x": 1.17, "y": 1.016, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.901, "y": 0.946, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 1.17, "y": 1.016, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 0.901, "y": 0.946, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.17, "y": 1.016, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "x": 0.901, "y": 0.946, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.17, "y": 1.016, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": 0.901, "y": 0.946, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.17, "y": 1.016, "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "x": 1.034, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.4667, "x": 1.435, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "curve": 0.25, "c3": 0.75}, {"time": 2.9667, "x": 1.435, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 3.1667, "x": 1.034, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 3.2, "curve": 0.25, "c3": 0.75}, {"time": 3.4667, "x": 1.435, "curve": 0.25, "c3": 0.75}, {"time": 3.7, "curve": 0.25, "c3": 0.75}, {"time": 3.9667, "x": 1.435, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 4.1667, "x": 1.034, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 4.3333, "x": 1.17, "y": 1.016}]}}, "deform": {"default": {"1-5zuiba": {"1-5zuiba": [{"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "offset": 2, "curve": 0.25, "c3": 0.75, "vertices": [-5.04861, 0.33693, -10.77194, 0.7189, -8.27995, 0.37295, -3.61775, 0.24144]}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "offset": 2, "curve": 0.25, "c3": 0.75, "vertices": [-5.04861, 0.33693, -10.77194, 0.7189, -8.27995, 0.37295, -3.61775, 0.24144]}, {"time": 3.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "offset": 2, "curve": 0.25, "c3": 0.75, "vertices": [-5.04861, 0.33693, -10.77194, 0.7189, -8.27995, 0.37295, -3.61775, 0.24144]}, {"time": 3.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "offset": 2, "curve": 0.25, "c3": 0.75, "vertices": [-5.04861, 0.33693, -10.77194, 0.7189, -8.27995, 0.37295, -3.61775, 0.24144]}, {"time": 4.1667}]}}}}}}