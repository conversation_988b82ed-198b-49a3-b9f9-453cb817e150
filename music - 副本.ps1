﻿# 极限32kbps压缩 - 高级选项
param(
    [string]$SourcePath = "F:\little\cocos_game\project_restore\assets\MusicLevel_5\Sound", #[string]$SourcePath = "F:\little\cocos_game\project_restore\assets",
    [string]$OutputFormat = "mp3",
    [switch]$DeleteOriginal = $true,
    [switch]$VoiceOptimized = $false
)

$ffmpegPath = "C:\Users\<USER>\Desktop\ffmpeg\bin\ffmpeg.exe"

# 检查FFmpeg是否存在
if (!(Test-Path $ffmpegPath)) {
    Write-Error "FFmpeg未找到，请先安装FFmpeg"
    exit 1
}

Write-Host "=== WAV到32kbps极限压缩工具 ===" -ForegroundColor Cyan
Write-Host "源目录: $SourcePath" -ForegroundColor Yellow
Write-Host "输出格式: $OutputFormat" -ForegroundColor Yellow
Write-Host ""

# 不同格式的压缩参数
$compressionSettings = @{
    "mp3" = @{
        codec = "libmp3lame"
        ext = "mp3"
        params = @("-b:a", "48k", "-ac", "1", "-ar", "22050")
    }
    "mp3_voice" = @{
        codec = "libmp3lame" 
        ext = "mp3"
        params = @("-b:a", "32k", "-ac", "1", "-ar", "16000", "-compression_level", "0", "-lowpass", "4000")
    }
    "aac" = @{
        codec = "aac"
        ext = "aac"
        params = @("-b:a", "32k", "-ac", "1", "-ar", "22050", "-profile:a", "aac_he")
    }
    "opus" = @{
        codec = "libopus"
        ext = "opus"
        params = @("-b:a", "32k", "-ac", "1", "-ar", "16000", "-compression_level", "10")
    }
    "amr" = @{
        codec = "libopencore_amr_nb"
        ext = "amr"
        params = @("-b:a", "12.2k", "-ac", "1", "-ar", "8000")  # AMR-NB for speech
    }
}

# 选择压缩设置
$settingKey = if ($VoiceOptimized) { "mp3_voice" } else { $OutputFormat }
$settings = $compressionSettings[$settingKey]

if (!$settings) {
    Write-Error "不支持的格式: $OutputFormat"
    Write-Host "支持的格式: $($compressionSettings.Keys -join ', ')"
    exit 1
}

# 获取所有音频文件（WAV和MP3）
$audioFiles = @()
$audioFiles += Get-ChildItem -Path $SourcePath -Filter "*.wav" -Recurse
$audioFiles += Get-ChildItem -Path $SourcePath -Filter "*.mp3" -Recurse

if ($audioFiles.Count -eq 0) {
    Write-Warning "未找到音频文件（WAV或MP3）"
    exit 0
}

$wavCount = ($audioFiles | Where-Object { $_.Extension -eq ".wav" }).Count
$mp3Count = ($audioFiles | Where-Object { $_.Extension -eq ".mp3" }).Count
Write-Host "找到 $($audioFiles.Count) 个音频文件 (WAV: $wavCount, MP3: $mp3Count)" -ForegroundColor Green
Write-Host ""

$totalOriginalSize = 0
$totalCompressedSize = 0
$successCount = 0

foreach ($file in $audioFiles) {
    # 根据文件扩展名生成输出路径
    $fileExt = $file.Extension.ToLower()
    $outputPath = $file.FullName -replace '\.(wav|mp3)$', "_32k.$($settings.ext)"
    
    Write-Host "处理: $($file.Name)" -NoNewline
    
    # 构建FFmpeg命令
    $ffmpegArgs = @("-i", $file.FullName, "-codec:a", $settings.codec) + $settings.params + @($outputPath, "-y")
    
    try {
        # 执行压缩
        $process = Start-Process -FilePath $ffmpegPath -ArgumentList $ffmpegArgs -NoNewWindow -Wait -PassThru
        
        if ($process.ExitCode -eq 0 -and (Test-Path $outputPath)) {
            $originalSize = $file.Length
            $compressedSize = (Get-Item $outputPath).Length
            $totalOriginalSize += $originalSize
            $totalCompressedSize += $compressedSize
            $successCount++

            $reduction = [math]::Round((1 - $compressedSize/$originalSize) * 100, 1)
            Write-Host " ✓ $([math]::Round($originalSize/1KB, 0))KB -> $([math]::Round($compressedSize/1KB, 0))KB (-$reduction%)" -ForegroundColor Green

            # 重命名文件，去掉_32k后缀
            $finalPath = $file.FullName -replace '\.(wav|mp3)$', ".$($settings.ext)"

            # 先删除原文件（如果指定且不会与目标文件冲突）
            $shouldDeleteOriginal = $DeleteOriginal -and ($file.FullName -ne $finalPath)
            if ($shouldDeleteOriginal) {
                Remove-Item $file.FullName -Force
                Write-Host "    已删除原文件" -ForegroundColor Yellow
            }

            # 如果目标文件存在且不是刚删除的原文件，则删除
            if ((Test-Path $finalPath) -and -not $shouldDeleteOriginal) {
                Remove-Item $finalPath -Force  # 删除已存在的目标文件
            }

            Rename-Item $outputPath $finalPath
            Write-Host "    已重命名为: $([System.IO.Path]::GetFileName($finalPath))" -ForegroundColor Cyan

            # 如果原文件和目标文件相同，且需要删除原文件，这里已经通过重命名完成了替换
        } else {
            Write-Host " ✗ 压缩失败" -ForegroundColor Red
        }
    }
    catch {
        Write-Host " ✗ 错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 显示统计信息
Write-Host ""
Write-Host "=== 压缩统计 ===" -ForegroundColor Cyan
Write-Host "成功处理: $successCount / $($audioFiles.Count) 个文件"
Write-Host "原始总大小: $([math]::Round($totalOriginalSize/1MB, 2)) MB"
Write-Host "压缩后总大小: $([math]::Round($totalCompressedSize/1MB, 2)) MB"
if ($totalOriginalSize -gt 0) {
    $totalReduction = [math]::Round((1 - $totalCompressedSize/$totalOriginalSize) * 100, 1)
    Write-Host "总体积减少: $totalReduction%" -ForegroundColor Green
    Write-Host "节省空间: $([math]::Round(($totalOriginalSize - $totalCompressedSize)/1MB, 2)) MB"
}

Write-Host ""
Write-Host "注意: 32kbps压缩会显著降低音质，建议仅用于:" -ForegroundColor Yellow
Write-Host "- 语音录音" 
Write-Host "- 对音质要求不高的场景"
Write-Host "- 需要极小文件体积的情况"