var n;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ChoiceData = undefined;
var $9AdInfo = require("AdInfo");
var $9AudioSourceCompont = require("AudioSourceCompont");
var $9LoadResModule = require("LoadResModule");
var $9UIManager = require("UIManager");
var $9XSdk = require("XSdk");
var $9GameControl = require("GameControl");
var $9ChoiceItem = require("ChoiceItem");
exports.ChoiceData = function () {
  this.num = 0;
  this.GAMEMODEl = 0;
  this.ads = 0;
};
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var def_ChoicePanel = function (e) {
  cc__extends(t, e);
  function t() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.choiceMgr = null;
    t.Privacy = null;
    t.data = [{
      num: 0,//经典
      GAMEMODEl: $9GameControl.GAMEMODEL.character,
      ads: 0
    }, {
      num: 1,//寄生虫 - 删除
      GAMEMODEl: $9GameControl.GAMEMODEL.cartoon,
      ads: 1
    }, {
      num: 0,//经典
      GAMEMODEl: $9GameControl.GAMEMODEL.cartoon,
      ads: 0
    }, {
      num: 1,//寄生虫
      GAMEMODEl: $9GameControl.GAMEMODEL.character,
      ads: 1
    }, {
      num: 2,//腐化
      GAMEMODEl: $9GameControl.GAMEMODEL.cartoon,
      ads: 2
    }, {
      num: 2,//腐化 - 删除
      GAMEMODEl: $9GameControl.GAMEMODEL.character,
      ads: 0
    }, {
      num: 3,//微恐
      GAMEMODEl: $9GameControl.GAMEMODEL.cartoon,
      ads: 1
    }, {
      num: 3,//微恐 - 删除
      GAMEMODEl: $9GameControl.GAMEMODEL.character,
      ads: 2
    }, {
      num: 4,//暗黑 - 删除
      GAMEMODEl: $9GameControl.GAMEMODEL.character,
      ads: 2
    }, {
      num: 4,//暗黑
      GAMEMODEl: $9GameControl.GAMEMODEL.cartoon,
      ads: 2
    }];
    return t;
  }
  t.prototype.start = function () {
    this.init();
  };
  t.prototype.show = function () {
    $9AdInfo.default.adsManager.showInterstitial();
    $9AudioSourceCompont.default.instance.playMusic($9AudioSourceCompont.AudioName.BackgroundsHome, $9GameControl.gameControl.bundleGame);
    $9GameControl.gameControl.isGameSceneName = "ChoicePanel";
    $9AdInfo.default.adsManager.hideIcon();
    $9AdInfo.default.adsManager.hideCustomAd(0);
    this.scheduleOnce(function () {
      $9AdInfo.default.adsManager.showIcon();
    }, 1);
    $9GameControl.gameControl.loadingCtrl.hide();
  };
  t.prototype.init = function () {


    var e = this;
    var t = [];
    for (var o = 0; o < $9AdInfo.default.data.length; o++) {
      t.push(this.data[$9AdInfo.default.data[o]]);
    }
    console.log('关卡信息', $9AdInfo.default.data);


    $9LoadResModule.default.loadBundleRes($9GameControl.gameControl.bundleGame, $9GameControl.PrefabName.ChoiceItem, function (o) {
      for (var n = 0; n < t.length; n++) {
        var i = cc.instantiate(o);
        i.parent = e.choiceMgr;
        i.getComponent($9ChoiceItem.default).Init(t[n], n);
      }
    }, cc.Prefab);
  };
  t.prototype.hide = function () {};
  t.prototype.btnShowSetting = function () {
    $9AudioSourceCompont.default.instance.playSound($9AudioSourceCompont.AudioName.ButtonClick, $9GameControl.gameControl.bundleGame);
    $9UIManager.default.showDialog($9GameControl.gameControl.bundleGame, $9GameControl.GamePanelName.SettingPanel, $9UIManager.RootType.Pop);
  };
  t.prototype.onClickPrivacyAgreementBtn = function () {
    $9XSdk.default.getInstance().onClickPrivacyAgreementBtn();
  };
  cc__decorate([ccp_property(cc.Node)], t.prototype, "choiceMgr", undefined);
  cc__decorate([ccp_property(cc.Node)], t.prototype, "Privacy", undefined);
  return cc__decorate([ccp_ccclass], t);
}(cc.Component);
exports.default = def_ChoicePanel;