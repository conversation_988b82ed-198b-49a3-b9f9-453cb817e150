{"skeleton": {"hash": "3hRVFR2Yhjb2sYCd/1uqkiginoo", "spine": "3.8.99", "x": -67.31, "y": -0.86, "width": 129.07, "height": 202.28, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/1-9"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "scaleX": 0.9632, "scaleY": 0.9632}, {"name": "bone2", "parent": "bone", "length": 102.54, "rotation": 89.8, "x": -0.36, "y": 0.53}, {"name": "bone2b", "parent": "bone2", "length": 102.54, "x": 102.54}, {"name": "bone2b2", "parent": "bone2b", "x": 48.99, "y": 28.63}, {"name": "bone2b3", "parent": "bone2b", "x": 49.18, "y": -27.21}], "slots": [{"name": "1-9<PERSON><PERSON>", "bone": "bone", "attachment": "1-9<PERSON><PERSON>"}, {"name": "1-9<PERSON><PERSON>", "bone": "bone", "attachment": "1-9<PERSON><PERSON>"}, {"name": "1-9yanjin2", "bone": "bone", "attachment": "1-9yanjin2"}, {"name": "1-9yanjin1", "bone": "bone", "attachment": "1-9yanjin1"}], "skins": [{"name": "default", "attachments": {"1-9shenti": {"1-9shenti": {"type": "mesh", "hull": 4, "width": 99, "height": 127, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -1.25, -50.49, 1, 1, 2, -1.59, 48.51, 1, 1, 2, 125.41, 48.95, 1, 1, 2, 125.75, -50.05, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-9tou": {"1-9tou": {"type": "mesh", "hull": 4, "width": 134, "height": 122, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -15.74, -64.19, 1, 1, 3, -16.2, 69.81, 1, 1, 3, 105.8, 70.23, 1, 1, 3, 106.26, -63.77, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-9yanjin1": {"1-9yanjin1": {"type": "mesh", "hull": 4, "width": 24, "height": 29, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 5, -14.01, -11.8, 1, 1, 5, -14.09, 12.2, 1, 1, 5, 14.91, 12.3, 1, 1, 5, 14.99, -11.7, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-9yanjin2": {"1-9yanjin2": {"type": "mesh", "hull": 4, "width": 25, "height": 26, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 4, -11, -12.63, 1, 1, 4, -11.09, 12.37, 1, 1, 4, 14.91, 12.46, 1, 1, 4, 15, -12.54, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"bones": {"bone2b": {"rotate": [{"angle": 1.85, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 1.85, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 1.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 1.85, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.85}], "translate": [{"x": 1.68, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 12.37, "y": 0.04, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": -1.82, "y": -0.01, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.68, "y": 0.01}]}, "bone2b2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 5.17, "y": 0.17, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -5.8, "y": 0.11, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 3.62, "y": -0.05, "curve": 0.25, "c3": 0.75}, {"time": 1}]}}}}}