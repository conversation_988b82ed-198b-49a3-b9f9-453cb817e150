{"skeleton": {"hash": "Umv5x8srncCUiLCrOnFqqGEK+iA", "spine": "3.8.99", "x": -71.22, "y": -2.05, "width": 131.42, "height": 219.33, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/系列2/2-9"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "scaleX": 0.888, "scaleY": 0.888}, {"name": "bone2", "parent": "bone", "length": 95.8, "rotation": 90.1, "x": 0.14, "y": 97.89}, {"name": "bone3", "parent": "bone2", "length": 27.85, "rotation": 6.76, "x": 101.35, "y": 9.39}, {"name": "bone4", "parent": "bone2", "x": 45.64, "y": 18.78}, {"name": "bone5", "parent": "bone2", "x": 46.68, "y": -23.45}, {"name": "bone6", "parent": "bone", "length": 95.8, "rotation": 90.1, "x": 0.31, "y": 0.17}], "slots": [{"name": "2-9<PERSON><PERSON>", "bone": "bone", "attachment": "2-9<PERSON><PERSON>"}, {"name": "2-9<PERSON><PERSON>", "bone": "bone", "attachment": "2-9<PERSON><PERSON>"}, {"name": "2-9<PERSON><PERSON>", "bone": "bone", "attachment": "2-9<PERSON><PERSON>"}, {"name": "2-9<PERSON><PERSON><PERSON>", "bone": "bone", "attachment": "2-9<PERSON><PERSON><PERSON>"}], "skins": [{"name": "default", "attachments": {"2-9shenti": {"2-9shenti": {"type": "mesh", "hull": 4, "width": 109, "height": 128, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 6, -2.58, -54.48, 1, 1, 6, -2.38, 54.52, 1, 1, 6, 125.62, 54.29, 1, 1, 6, 125.42, -54.71, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-9tou": {"2-9tou": {"type": "mesh", "hull": 33, "width": 148, "height": 157, "uvs": [0.75067, 0.08765, 0.77283, 0.27698, 0.67587, 0.29787, 0.77355, 0.34734, 0.8376, 0.42382, 0.86216, 0.55264, 0.90059, 0.53057, 0.96358, 0.52654, 1, 0.57686, 1, 0.64026, 0.97959, 0.71171, 0.94222, 0.73083, 0.96464, 0.77109, 0.93368, 0.88438, 0.78838, 0.96145, 0.64213, 1, 0.43182, 1, 0.27809, 0.94334, 0.21231, 0.83757, 0.14028, 0.85976, 0.05579, 0.84018, 0, 0.77228, 0, 0.66129, 0.05718, 0.59731, 0.14859, 0.59601, 0.16407, 0.47588, 0.22225, 0.38317, 0.31228, 0.31788, 0.20492, 0.34245, 0.13705, 0.29283, 0.19384, 0.10219, 0.33235, 0, 0.61769, 0, 0.16383, 0.67827, 0.18599, 0.75531, 0.44455, 0.27698, 0.55398, 0.27306, 0.86002, 0.6785], "triangles": [28, 29, 27, 27, 29, 30, 35, 30, 31, 32, 35, 31, 18, 27, 35, 32, 36, 35, 36, 32, 0, 14, 15, 37, 37, 15, 16, 37, 16, 17, 14, 37, 13, 5, 37, 3, 2, 3, 37, 37, 36, 2, 36, 37, 17, 36, 18, 35, 36, 17, 18, 13, 11, 12, 13, 37, 11, 18, 19, 34, 19, 20, 34, 34, 21, 33, 23, 33, 22, 21, 34, 20, 27, 18, 34, 23, 24, 33, 33, 21, 22, 27, 34, 33, 25, 26, 24, 11, 37, 10, 10, 37, 9, 9, 37, 5, 9, 5, 8, 8, 5, 6, 5, 3, 4, 26, 33, 24, 6, 7, 8, 1, 2, 0, 2, 36, 0, 33, 26, 27, 35, 27, 30], "vertices": [2, 2, 132.98, -31, 0.50014, 3, 26.65, -43.83, 0.49986, 2, 2, 103.25, -34.23, 0.7396, 3, -3.25, -43.53, 0.2604, 1, 2, 99.99, -19.87, 1, 2, 2, 92.2, -34.31, 0.99976, 3, -14.23, -42.32, 0.00024, 1, 2, 80.18, -43.77, 1, 1, 2, 59.94, -47.37, 1, 1, 2, 63.4, -53.06, 1, 1, 2, 64.01, -62.38, 1, 1, 2, 56.11, -67.76, 1, 1, 2, 46.15, -67.74, 1, 1, 2, 34.94, -64.7, 1, 1, 2, 31.95, -59.17, 1, 1, 2, 25.62, -62.47, 1, 1, 2, 7.84, -57.86, 1, 1, 2, -4.22, -36.33, 1, 1, 2, -10.23, -14.68, 1, 1, 2, -10.17, 16.45, 1, 1, 2, -1.24, 39.19, 1, 1, 2, 15.39, 48.89, 1, 1, 2, 11.92, 59.56, 1, 1, 2, 15.02, 72.06, 1, 1, 2, 25.69, 80.29, 1, 1, 2, 43.12, 80.26, 1, 1, 2, 53.15, 71.78, 1, 1, 2, 53.33, 58.25, 1, 1, 2, 72.18, 55.93, 1, 1, 2, 86.72, 47.29, 1, 1, 2, 96.95, 33.95, 1, 2, 2, 93.12, 49.84, 0.61041, 3, -3.41, 41.14, 0.38959, 2, 2, 100.93, 59.87, 0.31907, 3, 5.52, 50.19, 0.68093, 2, 2, 130.85, 51.41, 0.1785, 3, 34.23, 38.27, 0.8215, 2, 2, 146.85, 30.88, 0.18538, 3, 47.71, 15.99, 0.81462, 2, 2, 146.77, -11.35, 0.30537, 3, 42.67, -25.93, 0.69463, 1, 2, 40.41, 56.02, 1, 1, 2, 28.31, 52.76, 1, 1, 2, 103.34, 14.36, 1, 1, 2, 103.92, -1.84, 1, 1, 2, 40.18, -47.02, 1], "edges": [48, 66, 66, 68, 68, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 54, 52, 52, 50, 54, 70, 70, 72, 72, 4, 4, 2, 2, 0, 0, 64, 62, 64, 62, 60, 60, 58, 58, 56, 56, 54, 4, 6, 6, 8, 8, 10, 10, 74, 74, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 22, 24, 24, 26, 26, 28, 28, 30, 36, 34, 30, 32, 34, 32]}}, "2-9yanjin": {"2-9yanjin": {"type": "mesh", "hull": 4, "width": 24, "height": 31, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 5, -13.96, -14.27, 1, 1, 5, -13.91, 9.73, 1, 1, 5, 17.09, 9.68, 1, 1, 5, 17.04, -14.32, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-9yanzhao": {"2-9yanzhao": {"type": "mesh", "hull": 4, "width": 65, "height": 67, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 4, -36.86, -27.46, 1, 1, 4, -36.74, 37.54, 1, 1, 4, 30.26, 37.42, 1, 1, 4, 30.14, -27.58, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"bones": {"bone2": {"rotate": [{"angle": -12.32, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "angle": -12.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 8.67, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 1, "angle": -12.32}], "translate": [{"x": -0.1, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -1.54, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -1.54, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1, "x": -0.1}]}, "bone4": {"translate": [{"x": 0.85, "y": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.59, "y": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.85, "y": -3.75}]}, "bone5": {"translate": [{"x": 0.85, "y": -3.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.59, "y": 3.8, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.85, "y": -3.75}]}, "bone3": {"rotate": [{"angle": -16.77, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": -18.97, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 12.7, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": -16.77}]}}}}}