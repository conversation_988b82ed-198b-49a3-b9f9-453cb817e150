var n;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $9AdInfo = require("AdInfo");
var $912 = require("12");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var def_TouchMistake = function (e) {
  cc__extends(t, e);
  function t() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.InTheAreaLevel = -1;
    t.serverInTheArea = false;
    t.isTimeComple = false;
    t.ignoreArea = false;
    t.time1_1 = 20;
    t.time1_2 = 24;
    t.time2_1 = 19;
    t.time2_2 = 24;
    t.IsAdsSwitch = false;
    return t;
  }
  t.prototype.start = function () {
    cc.game.addPersistRootNode(this.node);
    this.node.zIndex = 100;
    this.node.active = false;
    globalThis.touchMistake = this;
    this.serverJudgmentArea();
    this.SetAdsSwitch();
  };
  t.prototype.serverJudgmentArea = function () {
    this.serverInTheArea = false;
    var e = this;
    $9AdInfo.default.isNative = false;
    $9AdInfo.default.isPush = false;
    "u8sdk" == globalThis.channel && (globalThis.isServerImg = true);
    (function () {
      var i = {
        "isServerImg": false,
        "isShowBanner": true,
        "isShowIcon": true,
        "isShowInit": true,
        "InitSpecialTime": 0,
        "serverJudgmentArea": false, // 是否开启误触
        "serverJudgmentAreaTime": 0,
        "MainInit": false,
        "isDressInit": true,
        "Test": false,
        "initTime": 0.2,
        "adsNum": 9999,
        "isGameDrawer": true,
        "isGmePortalAd": true,
        "initinterval": 10,
        "isShowInitBk": true,
        "InitTheAreaTime": 5,
        "time1_1": 20,
        "time1_2": 24,
        "time2_1": 19,
        "time2_2": 24,
        "isNative": true,
        "isWg": false,
        "ignoreAreaTime": 0,
        "data": [ 0,1,2,3,4,5,6,7,8,9 ] // 关卡配置 [2, 6, 9]   删除  1、5、7、8
      };
      globalThis.isServerImg = i.isServerImg;
      $9AdInfo.default.isShowBanner = i.isShowBanner;
      $9AdInfo.default.isShowInit = i.isShowInit;
      $9AdInfo.default.MainInit = i.MainInit;
      e.serverInTheArea = i.serverJudgmentArea;
      $9AdInfo.default.isDressInit = i.isDressInit;
      $9AdInfo.default.adsNum = i.adsNum;
      $9AdInfo.default.isShowIcon = i.isShowIcon;
      $9AdInfo.default.initTime = i.initTime;
      $9AdInfo.default.isGameDrawer = i.isGameDrawer;
      $9AdInfo.default.isGmePortalAd = i.isGmePortalAd;
      $9AdInfo.default.GiftTime = i.GiftTime;
      $9AdInfo.default.isGiftTimeShow = i.isGiftTimeShow;
      $9AdInfo.default.initinterval = i.initinterval;
      $9AdInfo.default.isShowInitBk = i.isShowInitBk;
      $9AdInfo.default.isNative = i.isNative;
      $9AdInfo.default.isPush = i.isPush;
      $9AdInfo.default.isWg = i.isWg;
      $9AdInfo.default.data = i.data;
      e.time1_1 = i.time1_1;
      e.time1_2 = i.time1_2;
      e.time2_1 = i.time2_1;
      e.time2_2 = i.time2_2;
      console.log("是否开启误触", e.serverInTheArea);
      e.serverInTheArea && e.JudgmentArea();

      return;
      var t = new XMLHttpRequest();
      t.onreadystatechange = function () {
        if (4 == t.readyState && t.status >= 200 && t.status < 400) {
          var o = t.responseText;
          console.log(o, "<<<response2");
          var n = JSON.parse(o);
          var i = n.level ? n[n.level] : n;
          var a = i.ignoreAreaTime || 1;
          "wx" == globalThis.channel && null != a && setTimeout(function () {
            e.ignoreArea = true;
            console.log("无视误触");
          }, 1e3 * a);
          var r = i.serverJudgmentAreaTime;
          setTimeout(function () {
            e.isTimeComple = true;
            console.log("成功");
          }, 1e3 * r);
          globalThis.isServerImg = i.isServerImg;
          $9AdInfo.default.isShowBanner = i.isShowBanner;
          $9AdInfo.default.isShowInit = i.isShowInit;
          $9AdInfo.default.MainInit = i.MainInit;
          e.serverInTheArea = i.serverJudgmentArea;
          $9AdInfo.default.isDressInit = i.isDressInit;
          $9AdInfo.default.adsNum = i.adsNum;
          $9AdInfo.default.isShowIcon = i.isShowIcon;
          $9AdInfo.default.initTime = i.initTime;
          $9AdInfo.default.isGameDrawer = i.isGameDrawer;
          $9AdInfo.default.isGmePortalAd = i.isGmePortalAd;
          $9AdInfo.default.GiftTime = i.GiftTime;
          $9AdInfo.default.isGiftTimeShow = i.isGiftTimeShow;
          $9AdInfo.default.initinterval = i.initinterval;
          $9AdInfo.default.isShowInitBk = i.isShowInitBk;
          $9AdInfo.default.isNative = i.isNative;
          $9AdInfo.default.isPush = i.isPush;
          $9AdInfo.default.isWg = i.isWg;
          $9AdInfo.default.data = i.data;
          e.time1_1 = i.time1_1;
          e.time1_2 = i.time1_2;
          e.time2_1 = i.time2_1;
          e.time2_2 = i.time2_2;
          console.log("是否开启误触", e.serverInTheArea);
          e.serverInTheArea && e.JudgmentArea();
        }
      };
      var o = "";
      if ("vivo" == globalThis.channel) {
        o = "https://rainbowbaby-1254205301.cos.ap-guangzhou.myqcloud.com/Mpl/jzhz/Json/vivo/jzhz3.17.json";
      } else if ("oppo" == globalThis.channel) {
        o = "https://rainbowbaby-1254205301.cos.ap-guangzhou.myqcloud.com/Mpl/jzhz/Json/oppo/jzhz3.17.json";
      } else if ("wx" == globalThis.channel) {
        o = "https://rainbowbaby-1254205301.cos.ap-guangzhou.myqcloud.com/Mpl/jzhz/Json/wx/jzhz2.17.json";
      } else if ("web" == globalThis.channel) {
        o = "https://rainbowbaby-1254205301.cos.ap-guangzhou.myqcloud.com/Mpl/jzhz/Json/vivo/jzhz3.17.json";
      } else if ("huawei" == globalThis.channel) {
        o = "https://rainbowbaby-1254205301.cos.ap-guangzhou.myqcloud.com/Mpl/aili/LULU/json/huawei/llsj12.12.json";
      } else if ("ks" == globalThis.channel) {
        o = "https://rainbowbaby-1254205301.cos.ap-guangzhou.myqcloud.com/Mpl/jzhz/Json/ks/jzhz2.17.json";
      } else if ("bl" == globalThis.channel) {
        o = "https://rainbowbaby-1254205301.cos.ap-guangzhou.myqcloud.com/Mpl/aili/LULU/json/bl/llsj1.8.json";
      } else if ("ry" == globalThis.channel) {
        o = "https://rainbowbaby-1254205301.cos.ap-guangzhou.myqcloud.com/Mpl/mhly/json/ry/mhly6.25.json";
      } else if ("apk" == globalThis.channel) {
        "vivo" == globalThis.subChannel && (o = "https://rainbowbaby-1254205301.cos.ap-guangzhou.myqcloud.com/Mpl/aili/LULU/json/Apk/vivo/llsj10.30.json");
      } else {
        "u8sdk" == globalThis.channel && (o = cc.sys.platform == cc.sys.DESKTOP_BROWSER ? "https://rainbowbaby-1254205301.cos.ap-guangzhou.myqcloud.com/Mpl/jzhz/Json/u8sdk/vivo/jzhz4.23.json" : "1008" == globalThis.subChannel ? "https://rainbowbaby-1254205301.cos.ap-guangzhou.myqcloud.com/Mpl/jzhz/Json/u8sdk/vivo/jzhz4.23.json" : "1007" == globalThis.subChannel ? "https://rainbowbaby-1254205301.cos.ap-guangzhou.myqcloud.com/Mpl/jzhz/Json/u8sdk/oppo/jzhz4.23.json" : "1012" == globalThis.subChannel ? "https://rainbowbaby-1254205301.cos.ap-guangzhou.myqcloud.com/Mpl/jzhz/Json/u8sdk/huawei/jzhz4.23.json" : "1005" == globalThis.subChannel ? "https://rainbowbaby-1254205301.cos.ap-guangzhou.myqcloud.com/Mpl/jzhz/Json/u8sdk/xm/jzhz4.23.json" : "https://rainbowbaby-1254205301.cos.ap-guangzhou.myqcloud.com/Mpl/jzhz/Json/u8sdk/wx/jzhz4.23.json");
      }
      console.log(globalThis.channel, "<<<<", o);
      t.open("GET", o, true);
      t.send();
    })();
  };
  t.prototype.JudgmentArea = function () {
    return;
    
    var e = ["北京", "成都", "广州", "深圳", "上海"];
    var t = ["惠州", "厦门", "郑州", "西安", "苏州", "济南", "台湾", "长沙", "澳门", "南京", "香港", "广东", "江门", "东莞", "杭州", "天津", "重庆", "武汉", "福州"];
    var o = new Date().getTime().toString();
    var n = o + "woe41sso64yyf1oc";
    var i = {
      xyx_timestamp: o,
      xyx_token: $912.MD5(n).toString()
    };
    var a = this;
    this.POST("https://wgg.yijustudio.com/xyx/getBarrierDataJson", function (o, n) {
      if (o) {
        var i = JSON.parse(n);
        if (200 != i.code) {
          a.InTheAreaLevel = 1;
        } else {
          var s = i.data.grass;
          for (var r = 0; r < e.length; r++) {
            if (-1 != s.indexOf(e[r])) {
              a.InTheAreaLevel = 1;
              break;
            }
          }
          for (r = 0; r < t.length; r++) {
            if (-1 != s.indexOf(t[r])) {
              a.InTheAreaLevel = 2;
              break;
            }
          }
        }
      } else {
        a.InTheAreaLevel = 1;
      }
      console.log("区域屏蔽>>>", a.InTheAreaLevel);
    }, i);
  };
  t.prototype.SetAdsSwitch = function () {
    return;
    var e = this;
    var t = new XMLHttpRequest();
    t.onreadystatechange = function () {
      if (4 == t.readyState && t.status >= 200 && t.status < 400) {
        var o = t.responseText;
        console.log(o, "<<<全局开关");
        var n = JSON.parse(o);
        e.IsAdsSwitch = n.IsAdsSwitch;
      }
    };
    t.open("GET", "https://rainbowbaby-1254205301.cos.ap-guangzhou.myqcloud.com/Mpl/AdsSwitch.json", true);
    t.send();
  };
  t.prototype.getTheArea = function () {
    console.log(this.InTheAreaLevel + "InTheAreaLevel");
    if (!this.serverInTheArea) {
      return false;
    }
    if (!this.isTimeComple) {
      return false;
    }
    if (this.ignoreArea) {
      return true;
    }
    if (this.IsAdsSwitch) {
      return true;
    }
    if ("oppo" == globalThis.channel) {
      var e = (o = new Date()).getDay();
      var t = o.getHours();
      console.log(this.InTheAreaLevel + "InTheAreaLevelday", e, t);
      if (1 == this.InTheAreaLevel) {
        return 6 == e || 0 == e || t >= this.time1_1 && t <= this.time1_2 || t >= 0 && t <= 8;
      } else {
        return 6 == e || 0 == e || t >= this.time2_1 && t <= this.time2_2 || t >= 0 && t <= 8;
      }
    }
    if (1 == this.InTheAreaLevel) {
      return false;
    }
    if (2 == this.InTheAreaLevel) {
      var o;
      e = (o = new Date()).getDay();
      t = o.getHours();
      console.log(this.InTheAreaLevel + "InTheAreaLevelday", e, t);
      return 6 == e || 0 == e || t >= this.time2_1 && t <= this.time2_2 || t >= 0 && t <= 8;
    }
    return true;
  };
  t.prototype.getAds = function () {
    var e = this.getTheArea();
    console.log(e, "<<<<<");
    return e;
  };
  t.prototype.playView = function () {
    console.log("show,违规");
    this.getAds() && 3 != $9AdInfo.default.noAd && $9AdInfo.default.adsManager.showVideo("", function () {});
  };
  t.prototype.POST = function (e, t, o) {
    undefined === o && (o = {});
    var n = "";
    Object.keys(o).forEach(function (e) {
      n += e + "=" + encodeURIComponent(o[e]) + "&";
    });
    "" !== n && (n = n.substr(0, n.lastIndexOf("&")));
    var i = cc.loader.getXMLHttpRequest();
    i.open("POST", e, true);
    i.setRequestHeader("Content-Type", "application/json");
    i.onreadystatechange = function () {
      if (4 === i.readyState) {
        var e = i.responseText;
        if (i.status >= 200 && i.status < 300) {
          i.statusText;
          t(true, e);
        } else {
          t(false, e);
        }
      }
    };
    var a = JSON.stringify(o);
    i.send(a);
  };
  return cc__decorate([ccp_ccclass], t);
}(cc.Component);
exports.default = def_TouchMistake;