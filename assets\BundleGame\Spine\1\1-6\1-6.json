{"skeleton": {"hash": "332PuP67OLzo+pFgEuj64+ktdNo", "spine": "3.8.99", "x": -83.62, "y": -6.12, "width": 168.61, "height": 207.6, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/1-6"}, "bones": [{"name": "root", "scaleX": 1.0538, "scaleY": 1.0538}, {"name": "bone", "parent": "root"}, {"name": "bone2", "parent": "bone", "length": 88.55, "rotation": 90, "x": -0.55, "y": -0.06}, {"name": "bone3", "parent": "bone2", "length": 68.26, "x": 87.93, "y": 0.61}, {"name": "bone4", "parent": "bone3", "length": 38.21, "rotation": 71.71, "x": 73.48, "y": 38.43}, {"name": "bone5", "parent": "bone3", "length": 37.25, "rotation": -73.21, "x": 76.25, "y": -42.43}, {"name": "bone6", "parent": "bone3", "x": 50.8, "y": -1.77}, {"name": "bone7", "parent": "bone3", "x": 7.4, "y": -2.53}, {"name": "bone8", "parent": "bone7", "length": 8.17, "rotation": 179.24, "x": -0.24, "y": -4.42}, {"name": "bone10", "parent": "bone6", "x": 6.51, "y": 24.16}], "slots": [{"name": "1-6<PERSON><PERSON>", "bone": "root", "attachment": "1-6<PERSON><PERSON>"}, {"name": "1-6<PERSON><PERSON>", "bone": "root", "attachment": "1-6<PERSON><PERSON>"}, {"name": "1-6<PERSON><PERSON>", "bone": "root", "attachment": "1-6<PERSON><PERSON>"}, {"name": "1-6<PERSON><PERSON>", "bone": "root", "attachment": "1-6<PERSON><PERSON>"}, {"name": "1-6yanpi1", "bone": "bone10"}], "skins": [{"name": "default", "attachments": {"1-6shenti": {"1-6shenti": {"type": "mesh", "hull": 4, "width": 124, "height": 146, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.65, -5.81, -61.35, -5.81, -61.35, 140.19, 62.65, 140.19], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-6tou": {"1-6tou": {"type": "mesh", "hull": 23, "width": 160, "height": 105, "uvs": [0.6788, 0.08145, 0.77844, 0.07578, 0.90416, 0.07719, 1, 0.08855, 1, 0.25316, 0.93675, 0.32978, 0.85573, 0.40925, 0.97338, 0.55524, 0.89609, 0.78655, 0.62695, 1, 0.42951, 1, 0.11475, 0.78946, 0.03932, 0.54565, 0.15087, 0.42088, 0.08289, 0.34567, 0.02515, 0.28039, 0, 0.20944, 0, 0.10301, 0.07544, 0.07037, 0.20675, 0.06895, 0.32874, 0.08456, 0.41535, 0, 0.58381, 0, 0.77844, 0.24606, 0.2561, 0.18957, 0.18812, 0.3187], "triangles": [2, 3, 4, 4, 5, 2, 16, 17, 18, 15, 16, 18, 14, 15, 18, 23, 1, 2, 5, 23, 2, 25, 18, 19, 25, 14, 18, 24, 19, 20, 20, 22, 0, 22, 20, 21, 9, 10, 23, 10, 24, 23, 11, 12, 13, 20, 23, 24, 25, 24, 10, 13, 25, 10, 11, 13, 10, 8, 6, 7, 23, 20, 0, 6, 9, 23, 8, 9, 6, 25, 19, 24, 13, 14, 25, 0, 1, 23, 6, 23, 5], "vertices": [1, 3, 94.77, -30.42, 1, 2, 3, 95.37, -46.36, 0.44794, 5, 9.29, 17.17, 0.55206, 2, 3, 95.22, -66.48, 0.11461, 5, 28.5, 11.21, 0.88539, 2, 3, 94.03, -81.81, 0.0021, 5, 42.84, 5.64, 0.9979, 2, 3, 76.74, -81.81, 0.11182, 5, 37.85, -10.9, 0.88818, 2, 3, 68.7, -71.69, 0.44515, 5, 25.83, -15.68, 0.55485, 1, 3, 60.35, -58.73, 1, 1, 3, 45.02, -77.55, 1, 1, 3, 20.74, -65.18, 1, 1, 3, -1.68, -22.12, 1, 1, 3, -1.68, 9.47, 1, 1, 3, 20.43, 59.83, 1, 1, 3, 46.03, 71.9, 1, 1, 3, 59.13, 54.05, 1, 2, 3, 67.03, 64.93, 0.445, 4, 23.13, 14.44, 0.555, 2, 3, 73.88, 74.16, 0.11166, 4, 34.05, 10.83, 0.88834, 2, 3, 81.33, 78.19, 0.00028, 4, 40.21, 5.02, 0.99972, 2, 3, 92.51, 78.19, 0.00178, 4, 43.72, -5.59, 0.99822, 2, 3, 95.93, 66.12, 0.11467, 4, 33.33, -12.63, 0.88533, 2, 3, 96.08, 45.11, 0.448, 4, 13.43, -19.36, 0.552, 1, 3, 94.44, 25.59, 1, 1, 3, 103.32, 11.73, 1, 1, 3, 103.32, -15.22, 1, 1, 3, 77.49, -46.36, 1, 1, 3, 83.42, 37.21, 1, 1, 3, 69.86, 48.09, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 46, 46, 0, 0, 44, 42, 44, 40, 42, 40, 48, 48, 50, 50, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 26, 24, 24, 22, 22, 20, 12, 14, 14, 16, 18, 20, 16, 18]}}, "1-6yanjin": {"1-6yanjin": {"type": "mesh", "hull": 4, "width": 68, "height": 24, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 6, -12.48, -34.04, 1, 1, 6, -12.48, 33.96, 1, 1, 6, 11.52, 33.96, 1, 1, 6, 11.52, -34.04, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-6yanpi1": {"1-6yanpi1": {"type": "mesh", "hull": 4, "width": 16, "height": 7, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-0.98, -8.2, -0.98, 7.8, 6.02, 7.8, 6.02, -8.2], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}, "1-6yanpi2": {"type": "mesh", "hull": 4, "width": 21, "height": 16, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-9.98, -10.2, -9.98, 10.8, 6.02, 10.8, 6.02, -10.2], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}, "1-6yanpi3": {"type": "mesh", "hull": 4, "width": 20, "height": 27, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-20.98, -10.2, -20.98, 9.8, 6.02, 9.8, 6.02, -10.2], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-6zuiba": {"1-6zuiba": {"type": "mesh", "hull": 10, "width": 17, "height": 15, "uvs": [1, 0.19737, 1, 0.4439, 1, 0.75923, 1, 1, 0.58294, 1, 0.58058, 0.73609, 0.57788, 0.43243, 0, 0.4267, 0, 0, 1, 0, 0.60317, 0.2031], "triangles": [4, 2, 3, 4, 5, 2, 5, 1, 2, 5, 6, 1, 0, 10, 9, 6, 7, 10, 7, 8, 10, 10, 8, 9, 6, 10, 1, 10, 0, 1], "vertices": [1, 7, 0.96, -8.28, 1, 2, 7, -2.74, -8.28, 0.44444, 8, 2.45, 3.89, 0.55556, 2, 7, -7.47, -8.28, 0.11111, 8, 7.18, 3.95, 0.88889, 2, 7, -11.08, -8.28, 0.00053, 8, 10.79, 4, 0.99947, 2, 7, -11.08, -1.19, 0.11218, 8, 10.88, -3.09, 0.88782, 2, 7, -7.12, -1.15, 0.44551, 8, 6.92, -3.18, 0.55449, 1, 7, -2.57, -1.1, 1, 1, 7, -2.48, 8.72, 1, 1, 7, 3.92, 8.72, 1, 1, 7, 3.92, -8.28, 1, 1, 7, 0.87, -1.53, 1], "edges": [16, 18, 14, 16, 14, 12, 6, 8, 12, 20, 0, 18, 20, 0, 0, 2, 2, 4, 4, 6, 8, 10, 10, 12]}}}}], "animations": {"animation": {"slots": {"1-6yanpi1": {"attachment": [{"name": "1-6yanpi1"}, {"time": 2.3333, "name": "1-6yanpi3"}]}}, "bones": {"bone3": {"translate": [{"y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": -1.32, "curve": "stepped"}, {"time": 2.3333, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 3, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "y": -1.32}]}, "bone4": {"rotate": [{"angle": 9.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 19.01, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 9.39, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 19.01, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 9.39}, {"time": 2.3333, "angle": 19.01}]}, "bone5": {"rotate": [{"angle": -11.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -11.71, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -11.71}, {"time": 2.3333, "angle": -19.1}]}, "bone8": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -2.84, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -2.84, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": "stepped"}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "x": -2.84, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": -2.84, "curve": 0.25, "c3": 0.75}, {"time": 4.3333}]}, "bone6": {"translate": [{"time": 2.3333, "x": -2.75}]}, "bone7": {"translate": [{"time": 2.3333, "x": -0.65}]}}, "deform": {"default": {"1-6tou": {"1-6tou": [{"time": 2.3333, "vertices": [-5.34057, 0, -5.34057, 0, 0.21473, -5.33635, -5.34057, 0, 0.21473, -5.33635, -5.34057, 0, 0.21473, -5.33635, -5.34057, 0, 0.21473, -5.33635, -5.34057, 0, 0.21473, -5.33635, -5.34057, 0, -5.34057, 0, -5.34057, 0, -5.34057, 0, -5.34057, 0, -5.34057, 0, -5.34057, 0, -5.34057, 0, -5.34057, 0, 0.06722, 5.33992, -5.34057, 0, 0.06722, 5.33992, -5.34057, 0, 0.06722, 5.33992, -5.34057, 0, 0.06722, 5.33992, -5.34057, 0, 0.06722, 5.33992, -5.34057, 0, 0.06722, 5.33992, -5.34057, 0, -5.34057, 0, -5.34057, 0, -5.34057, 0, -5.34057, 0, -5.34057]}]}, "1-6yanjin": {"1-6yanjin": [{"time": 2.3333, "vertices": [-2.75569, 0, -2.75569, 0, -2.75569, 0, -2.75569]}]}, "1-6yanpi1": {"1-6yanpi3": [{"time": 2.3333, "vertices": [-2.75569, 0, -2.75569, 0, -2.75569, 0, -2.75569]}]}, "1-6zuiba": {"1-6zuiba": [{"time": 2.3333, "vertices": [-2.75569, 0, -2.75569, 0, 2.7554, 0.03649, -2.75569, 0, 2.7554, 0.03649, -2.75569, 0, 2.7554, 0.03649, -2.75569, 0, 2.7554, 0.03649, -2.75569, 0, 2.7554, 0.03649, -2.75569, 0, -2.75569, 0, -2.75569, 0, -2.75569, 0, -2.75569]}]}}}}}}