{"skeleton": {"hash": "Gu3dcW2l1Dy1BIV50u8xtcuLiPQ", "spine": "3.8.99", "x": -61.23, "y": -0.87, "width": 130.01, "height": 160.2, "images": "./images/", "audio": "E:/外包/2.15音乐游戏/系列5/5-9"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "scaleX": 0.8388, "scaleY": 0.8388}, {"name": "bone2", "parent": "bone", "length": 74.21, "rotation": 90, "x": -0.31, "y": 0.17}, {"name": "bone3", "parent": "bone2", "x": 106.86, "y": -3.91}, {"name": "bone4", "parent": "bone2", "x": 61.38, "y": -0.28}], "slots": [{"name": "5-9<PERSON><PERSON>", "bone": "bone", "attachment": "5-9<PERSON><PERSON>"}, {"name": "5-9<PERSON><PERSON>", "bone": "bone", "attachment": "5-9<PERSON><PERSON>"}, {"name": "5-9<PERSON><PERSON>", "bone": "bone", "attachment": "5-9<PERSON><PERSON>"}, {"name": "5-9<PERSON><PERSON>", "bone": "bone", "attachment": "5-9<PERSON><PERSON>"}], "skins": [{"name": "default", "attachments": {"5-9jiao": {"5-9jiao": {"type": "mesh", "hull": 4, "width": 130, "height": 38, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 1, 66, -1.04, 1, 1, 1, -64, -1.04, 1, 1, 1, -64, 36.96, 1, 1, 1, 66, 36.96, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "5-9shenti": {"5-9shenti": {"type": "mesh", "hull": 4, "width": 146, "height": 169, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, 20.79, -82.31, 1, 1, 2, 20.79, 63.69, 1, 1, 2, 189.79, 63.69, 1, 1, 2, 189.79, -82.31, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "5-9yanjin": {"5-9yanjin": {"type": "mesh", "hull": 4, "width": 99, "height": 40, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -19.07, -47.4, 1, 1, 3, -19.07, 51.6, 1, 1, 3, 20.93, 51.6, 1, 1, 3, 20.93, -47.4, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "5-9zuiba": {"5-9zuiba": {"type": "mesh", "hull": 4, "width": 148, "height": 35, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 4, -20.59, -75.03, 1, 1, 4, -20.59, 72.97, 1, 1, 4, 14.41, 72.97, 1, 1, 4, 14.41, -75.03, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"bones": {"bone2": {"translate": [{"y": -11.72, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": -6.98, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "y": -15.07, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "y": -11.72}]}, "bone4": {"translate": [{"x": -5.58, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 6.7, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -5.58}]}, "bone3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 2.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -2.51, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}}}}}