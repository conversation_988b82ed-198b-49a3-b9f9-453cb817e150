{"skeleton": {"hash": "XePQo35khkpHTbdc+E8wkvkVZdM", "spine": "3.8.99", "x": -58.74, "y": -2.27, "width": 132.68, "height": 207.16, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/系列2/2-17"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "scaleX": 0.7759, "scaleY": 0.7759}, {"name": "bone2", "parent": "bone", "length": 105.1, "rotation": 90, "x": 0.99, "y": 2.17}, {"name": "bone2b", "parent": "bone2", "length": 105.1, "x": 108.06}, {"name": "bone2b2", "parent": "bone2b", "length": 34.37, "rotation": 35.05, "x": 77.5, "y": 35.11}, {"name": "bone2b3", "parent": "bone2b2", "length": 41.16, "rotation": -16.61, "x": 35.34}, {"name": "bone2b4", "parent": "bone2b", "length": 28.07, "rotation": -38.93, "x": 82.68, "y": -35.27}, {"name": "bone2b5", "parent": "bone2b4", "length": 37.9, "rotation": 23.51, "x": 28.86}, {"name": "bone2b6", "parent": "bone2b", "x": 56.83, "y": 26.53}, {"name": "bone2b7", "parent": "bone2b", "x": 57.54, "y": -23.43}, {"name": "bone2b8", "parent": "bone2b", "x": 85.69, "y": 17.38}, {"name": "bone2b9", "parent": "bone2b", "x": 86.39, "y": -18.16}], "slots": [{"name": "2-17<PERSON><PERSON>", "bone": "bone", "attachment": "2-17<PERSON><PERSON>"}, {"name": "2-17<PERSON><PERSON>", "bone": "bone", "attachment": "2-17<PERSON><PERSON>"}, {"name": "2-17<PERSON><PERSON>2", "bone": "bone", "attachment": "2-17<PERSON><PERSON>2"}, {"name": "2-17<PERSON><PERSON>1", "bone": "bone", "attachment": "2-17<PERSON><PERSON>1"}], "skins": [{"name": "default", "attachments": {"2-17shenti": {"2-17shenti": {"type": "mesh", "hull": 4, "width": 105, "height": 147, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -5.09, -54.3, 1, 1, 2, -5.09, 50.7, 1, 1, 2, 141.91, 50.7, 1, 1, 2, 141.91, -54.3, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-17tou": {"2-17tou": {"type": "mesh", "hull": 23, "width": 171, "height": 153, "uvs": [0.89572, 0.09117, 0.90508, 0.20882, 0.90976, 0.31862, 0.89455, 0.45326, 0.84192, 0.52647, 0.92476, 0.50202, 1, 0.74776, 1, 1, 0, 1, 0, 0.47846, 0.10727, 0.53293, 0.04996, 0.43489, 0.03476, 0.24535, 0.15406, 0.15384, 0.27102, 0.16299, 0.37876, 0.27026, 0.45128, 0.24411, 0.57668, 0.26334, 0.64686, 0.19405, 0.72171, 0.12347, 0.78604, 0.06595, 0.82814, 0, 0.87584, 0, 0.77642, 0.43366, 0.67116, 0.33431, 0.26985, 0.34992, 0.17277, 0.43489], "triangles": [11, 12, 26, 26, 12, 13, 3, 23, 2, 1, 24, 19, 0, 1, 20, 2, 24, 1, 1, 19, 20, 20, 21, 0, 21, 22, 0, 24, 18, 19, 4, 10, 26, 4, 26, 25, 8, 9, 10, 7, 4, 6, 4, 5, 6, 17, 18, 24, 4, 25, 15, 16, 17, 24, 23, 4, 16, 16, 4, 15, 7, 8, 4, 16, 24, 23, 4, 8, 10, 25, 14, 15, 23, 24, 2, 4, 23, 3, 10, 11, 26, 25, 26, 13, 25, 13, 14], "vertices": [2, 6, 70.4, 3.9, 3e-05, 7, 39.65, -12.99, 0.99997, 3, 3, 121.9, -78.07, 6e-05, 6, 57.4, -8.66, 0.02864, 7, 22.72, -19.32, 0.97131, 3, 3, 105.1, -78.87, 0.11122, 6, 44.83, -19.84, 0.05724, 7, 6.74, -24.56, 0.83154, 3, 3, 84.5, -76.27, 0.44456, 6, 27.17, -30.76, 0.0572, 7, -13.81, -27.53, 0.49824, 1, 3, 73.3, -67.27, 1, 1, 3, 77.04, -81.44, 1, 1, 3, 39.44, -94.3, 1, 1, 3, 0.85, -94.3, 1, 1, 3, 0.85, 76.7, 1, 1, 3, 80.64, 76.7, 1, 1, 3, 72.31, 58.35, 1, 3, 3, 87.31, 68.15, 0.44954, 4, 27.01, 21.42, 0.15219, 5, -14.11, 18.15, 0.39828, 3, 3, 116.31, 70.75, 0.14318, 4, 52.24, 6.9, 0.26606, 5, 14.23, 11.44, 0.59076, 3, 3, 130.31, 50.35, 0.17016, 4, 51.99, -17.85, 0.32392, 5, 21.06, -12.34, 0.50592, 3, 3, 128.91, 30.35, 0.4984, 4, 39.35, -33.41, 0.22775, 5, 13.4, -30.87, 0.27386, 1, 3, 112.5, 11.93, 1, 1, 3, 116.5, -0.47, 1, 1, 3, 113.56, -21.91, 1, 3, 3, 124.16, -33.91, 0.46886, 6, 31.41, 27.11, 0.1926, 7, 13.15, 23.85, 0.33854, 3, 3, 134.96, -46.71, 0.13674, 6, 47.86, 23.94, 0.21389, 7, 26.97, 14.38, 0.64937, 3, 3, 143.76, -57.71, 0.01343, 6, 61.61, 20.91, 0.11847, 7, 38.38, 6.11, 0.86809, 3, 3, 153.85, -64.91, 0.00122, 6, 73.99, 21.65, 0.02187, 7, 50.02, 1.86, 0.9769, 3, 3, 153.85, -73.07, 1e-05, 6, 79.11, 15.31, 0.00059, 7, 52.19, -6.01, 0.9994, 1, 3, 87.5, -56.07, 1, 1, 3, 102.7, -38.07, 1, 1, 3, 100.31, 30.55, 1, 1, 3, 87.31, 47.15, 1], "edges": [14, 16, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 46, 46, 48, 48, 34, 34, 32, 32, 30, 30, 50, 50, 52, 52, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 16, 18, 20, 18, 8, 10, 14, 12, 10, 12]}}, "2-17yanjin1": {"2-17yanjin1": {"type": "mesh", "hull": 4, "width": 40, "height": 47, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 8, -28.99, -19.83, 1, 1, 8, -28.99, 20.17, 1, 1, 8, 18.01, 20.17, 1, 1, 8, 18.01, -19.83, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-17yanjin2": {"2-17yanjin2": {"type": "mesh", "hull": 4, "width": 34, "height": 42, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 9, -23.69, -23.87, 1, 1, 9, -23.69, 10.13, 1, 1, 9, 18.31, 10.13, 1, 1, 9, 18.31, -23.87, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"bones": {"bone2b": {"rotate": [{"angle": 11.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -11.12, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 11.64}], "translate": [{"x": -8.99, "y": -11.15, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 0.84, "y": 5.47, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -11.66, "y": 16.28, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": 0.84, "y": 0.51, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -8.99, "y": -11.15}]}, "bone2b6": {"translate": [{"x": -5.23, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "x": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.3333, "x": -5.23}]}, "bone2b7": {"translate": [{"x": -5.23, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "x": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -5.79, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1.3333, "x": -5.23}]}, "bone2b8": {"translate": [{"x": 4.07, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "x": 6.24, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -1.4, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 6.24, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": -1.4, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "x": 4.07}]}, "bone2b9": {"translate": [{"x": 4.07, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "x": 6.24, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -1.4, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 6.24, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": -1.4, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.3333, "x": 4.07}]}, "bone2b2": {"rotate": [{"angle": 3.68, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "angle": 4.64, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -6.94, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.3333, "angle": 3.68}]}, "bone2b3": {"rotate": [{"angle": 22.82, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": 32.33, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -6.94, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": 22.82}]}, "bone2b4": {"rotate": [{"angle": -9.06, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "angle": -10.71, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 9.25, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 1.3333, "angle": -9.06}]}, "bone2b5": {"rotate": [{"angle": -18.32, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2, "angle": -27.14, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 9.25, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.3333, "angle": -18.32}]}}}}}