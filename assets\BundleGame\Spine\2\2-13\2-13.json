{"skeleton": {"hash": "gYVxxsq7b5M4j3NVjAsKTEY8Hnk", "spine": "3.8.99", "x": -101.8, "y": -2.54, "width": 203, "height": 223, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/系列2/2-13"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "bone2", "parent": "bone", "length": 103.76, "rotation": 89.84, "x": 0.24, "y": -0.07}, {"name": "bone2b", "parent": "bone2", "length": 103.76, "x": 99.33}, {"name": "bone2b2", "parent": "bone2b", "length": 57.3, "rotation": 66.24, "x": 48.98, "y": 32.05}, {"name": "bone2b3", "parent": "bone2b", "length": 52.06, "rotation": -70.85, "x": 51.45, "y": -40.89}, {"name": "bone2b4", "parent": "bone2b", "length": 46.44, "rotation": 39.66, "x": 77.29, "y": 19.12}, {"name": "bone2b5", "parent": "bone2b", "length": 44.98, "rotation": -40.22, "x": 76.28, "y": -22.35}], "slots": [{"name": "2-13<PERSON><PERSON>", "bone": "bone", "attachment": "2-13<PERSON><PERSON>"}, {"name": "2-13<PERSON><PERSON>", "bone": "bone", "attachment": "2-13<PERSON><PERSON>"}], "skins": [{"name": "default", "attachments": {"2-13shenti": {"2-13shenti": {"type": "mesh", "hull": 4, "width": 120, "height": 120, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -2.31, -55.97, 1, 1, 2, -2.66, 64.03, 1, 1, 2, 117.34, 64.37, 1, 1, 2, 117.69, -55.63, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-13tou": {"2-13tou": {"type": "mesh", "hull": 4, "width": 203, "height": 127, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -5.52, -100.7, 1, 1, 3, -6.1, 102.3, 1, 1, 3, 120.9, 102.66, 1, 1, 3, 121.48, -100.34, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"bones": {"bone2b": {"rotate": [{"angle": 11.47, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1, "angle": 12.53, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -12.2, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 2, "angle": 11.47}], "translate": [{"x": 0.86, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "x": 1.57, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -3.94, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": 1.57, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": -3.94, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "x": 0.86}]}, "bone2b2": {"rotate": [{"angle": 30.53, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 34.73, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -8.85, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 30.53}], "translate": [{"y": 1.97, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "y": 3.03, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "y": -7.99, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "y": 1.97}]}, "bone2b3": {"rotate": [{"angle": -27.67, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -30.84, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 1.98, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -27.67}], "translate": [{"y": 6.91, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "y": 8.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "y": -7.99, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "y": 6.91}]}, "bone2b4": {"rotate": [{"angle": 9.09, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": 11.12, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -9.93, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 9.09}]}, "bone2b5": {"rotate": [{"angle": -5.59, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "angle": -6.74, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 5.22, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -5.59}]}}}}}