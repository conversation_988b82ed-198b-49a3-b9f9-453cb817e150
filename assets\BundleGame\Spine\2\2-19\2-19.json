{"skeleton": {"hash": "aazSJRzsAmFNcsaORpF8uRpZLIA", "spine": "3.8.99", "x": -87.82, "y": -8.27, "width": 172.69, "height": 233.09, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/系列2/2-19"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "scaleX": 0.9437, "scaleY": 0.9437}, {"name": "bone2", "parent": "root", "length": 98.16, "rotation": 90, "x": 0.44, "y": 0.21}, {"name": "bone2b", "parent": "bone2", "length": 98.16, "x": 96.07}, {"name": "bone2b2", "parent": "bone2b", "x": 52.12, "y": 24.56}, {"name": "bone2b3", "parent": "bone2b", "x": 52.12, "y": -20.86}, {"name": "bone2b4", "parent": "bone2b", "x": 74.3, "y": 22.42}, {"name": "bone2b5", "parent": "bone2b", "x": 75.1, "y": -19.53}], "slots": [{"name": "2-19<PERSON><PERSON>", "bone": "bone", "attachment": "2-19<PERSON><PERSON>"}, {"name": "2-19<PERSON>in2", "bone": "bone", "attachment": "2-19<PERSON>in2"}, {"name": "2-19<PERSON><PERSON>", "bone": "bone", "attachment": "2-19<PERSON><PERSON>"}, {"name": "2-19<PERSON>in1", "bone": "bone", "attachment": "2-19<PERSON>in1"}, {"name": "2-19meimao2", "bone": "bone", "attachment": "2-19meimao2"}, {"name": "2-19meimao1", "bone": "bone", "attachment": "2-19meimao1"}], "skins": [{"name": "default", "attachments": {"2-19meimao1": {"2-19meimao1": {"type": "mesh", "hull": 4, "width": 24, "height": 12, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 6, -4.27, -13.44, 1, 1, 6, -4.27, 9.21, 1, 1, 6, 7.05, 9.21, 1, 1, 6, 7.05, -13.44, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-19meimao2": {"2-19meimao2": {"type": "mesh", "hull": 4, "width": 20, "height": 10, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 7, -5.08, -15.84, 1, 1, 7, -5.08, 3.03, 1, 1, 7, 4.36, 3.03, 1, 1, 7, 4.36, -15.84, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-19shenti": {"2-19shenti": {"type": "mesh", "hull": 4, "width": 124, "height": 105, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -8.48, -58.96, 1, 1, 2, -8.48, 58.06, 1, 1, 2, 90.6, 58.06, 1, 1, 2, 90.6, -58.96, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-19shipin1": {"2-19shipin1": {"type": "mesh", "hull": 4, "width": 183, "height": 74, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -23.4, -84.44, 1, 1, 3, -23.4, 88.26, 1, 1, 3, 46.43, 88.26, 1, 1, 3, 46.43, -84.44, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-19shipin2": {"2-19shipin2": {"type": "mesh", "hull": 4, "width": 150, "height": 124, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, 11.52, -68.39, 1, 1, 3, 11.52, 73.16, 1, 1, 3, 128.53, 73.16, 1, 1, 3, 128.53, -68.39, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-19tou": {"2-19tou": {"type": "mesh", "hull": 4, "width": 155, "height": 196, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -79.07, -70.28, 1, 1, 3, -79.07, 75.99, 1, 1, 3, 105.89, 75.99, 1, 1, 3, 105.89, -70.28, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"bones": {"bone2b4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 28.68, "curve": "stepped"}, {"time": 1.2333, "angle": 28.68, "curve": 0.25, "c3": 0.75}, {"time": 1.5}], "translate": [{"y": -2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "y": -0.88, "curve": "stepped"}, {"time": 1.2333, "y": -0.88, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "y": -2.34}]}, "bone2b": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -1.58, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.58, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -1.58, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -1.58, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -1.58, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -1.58, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -1.58, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1.5}], "translate": [{"x": -10.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": "stepped"}, {"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -10.46}]}, "bone2b2": {"translate": [{"x": -6.8, "y": -4.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "y": -3.59, "curve": "stepped"}, {"time": 1.2333, "y": -3.59, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -6.8, "y": -4.65}]}, "bone2b3": {"translate": [{"x": -6.8, "y": -4.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "y": -3.59, "curve": "stepped"}, {"time": 1.2333, "y": -3.59, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -6.8, "y": -4.65}]}, "bone2b5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -37.5, "curve": "stepped"}, {"time": 1.2333, "angle": -37.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5}], "translate": [{"y": -2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "y": -2.94, "curve": "stepped"}, {"time": 1.2333, "y": -2.94, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "y": -2.34}]}}}}}