{"skeleton": {"hash": "bwaowWBo/o+3mzmzryOKhb+GBxU", "spine": "3.8.99", "x": -69.16, "y": -3.28, "width": 137.09, "height": 199.82, "images": "./images/", "audio": "E:/外包/2.15音乐游戏/系列3/3-19"}, "bones": [{"name": "root", "scaleX": 0.7745, "scaleY": 0.7745}, {"name": "bone", "parent": "root", "length": 124.07, "rotation": 90.09, "x": 0.54, "y": 2.02}, {"name": "bone2", "parent": "bone", "length": 124.07, "x": 124.07}, {"name": "bone3", "parent": "bone2", "x": 58.76, "y": 29.36}, {"name": "bone4", "parent": "bone2", "x": 59.81, "y": -29.16}, {"name": "bone5", "parent": "bone2", "x": 22.61, "y": 0.54}], "slots": [{"name": "3-19<PERSON><PERSON>", "bone": "root", "attachment": "3-19<PERSON><PERSON>"}, {"name": "3-19<PERSON><PERSON>", "bone": "root", "attachment": "3-19<PERSON><PERSON>"}, {"name": "3-19<PERSON><PERSON>", "bone": "root", "attachment": "3-19<PERSON><PERSON>"}, {"name": "3-19<PERSON>jin2", "bone": "root", "attachment": "3-19<PERSON>jin2"}, {"name": "3-19<PERSON>jin1", "bone": "root", "attachment": "3-19<PERSON>jin1"}], "skins": [{"name": "default", "attachments": {"3-19shenti": {"3-19shenti": {"type": "mesh", "hull": 4, "width": 120, "height": 195, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 1, -6.35, -58.16, 1, 1, 1, -6.17, 61.84, 1, 1, 1, 188.83, 61.55, 1, 1, 1, 188.65, -58.45, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "3-19tou": {"3-19tou": {"type": "mesh", "hull": 4, "width": 177, "height": 128, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -0.46, -87.36, 1, 1, 2, -0.19, 89.64, 1, 1, 2, 127.81, 89.45, 1, 1, 2, 127.54, -87.55, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "3-19yanjin1": {"3-19yanjin1": {"type": "mesh", "hull": 4, "width": 46, "height": 46, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -19.08, -22.78, 1, 1, 3, -19.01, 23.22, 1, 1, 3, 26.99, 23.15, 1, 1, 3, 26.92, -22.85, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "3-19yanjin2": {"3-19yanjin2": {"type": "mesh", "hull": 4, "width": 46, "height": 46, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 4, -20.21, -22.25, 1, 1, 4, -20.14, 23.75, 1, 1, 4, 25.86, 23.67, 1, 1, 4, 25.79, -22.33, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "3-19zuiba": {"3-19zuiba": {"type": "mesh", "hull": 4, "width": 42, "height": 42, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 5, -29.97, -20.88, 1, 1, 5, -29.91, 21.12, 1, 1, 5, 12.09, 21.06, 1, 1, 5, 12.03, -20.94, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"bones": {"bone2": {"rotate": [{"angle": 1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -2.13, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -2.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -2.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -2.13, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -2.13, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -2.13, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -2.13, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 1.49, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.13, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 1.49, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -2.13, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 1.49, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -2.13, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.04}], "translate": [{"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -4.8, "y": 0.01, "curve": "stepped"}, {"time": 1.5, "x": -4.8, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "bone5": {"scale": [{"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": 0.501, "y": 0.545, "curve": "stepped"}, {"time": 1.5, "x": 0.501, "y": 0.545, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "bone3": {"translate": [{"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -4.8, "y": 0.01, "curve": "stepped"}, {"time": 1.5, "x": -4.8, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "bone4": {"translate": [{"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "x": -4.8, "y": 0.01, "curve": "stepped"}, {"time": 1.5, "x": -4.8, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}}}}}