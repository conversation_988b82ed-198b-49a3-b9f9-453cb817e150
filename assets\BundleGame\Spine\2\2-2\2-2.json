{"skeleton": {"hash": "***************************", "spine": "3.8.99", "x": -76.89, "y": -2.3, "width": 152.54, "height": 203.75, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/系列2/2-2"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "scaleX": 1.0035, "scaleY": 1.0035}, {"name": "bone2", "parent": "bone", "length": 72.91, "rotation": 89.81, "x": -0.09, "y": 0.32}, {"name": "bone3", "parent": "bone2", "length": 53.38, "rotation": 0.99, "x": 74.3}, {"name": "bone4", "parent": "bone3", "x": 36.06, "y": 0.09}, {"name": "bone5", "parent": "bone3", "x": 9.23, "y": -0.14}, {"name": "bone6", "parent": "bone3", "x": 62.29, "y": 0.32}], "slots": [{"name": "2-2<PERSON><PERSON>", "bone": "bone", "attachment": "2-2<PERSON><PERSON>"}, {"name": "2-2<PERSON><PERSON>", "bone": "bone", "attachment": "2-2<PERSON><PERSON>"}, {"name": "2-2yanjin2", "bone": "bone", "attachment": "2-2yanjin2"}, {"name": "2-2yanjin1", "bone": "bone", "attachment": "2-2yanjin1"}, {"name": "2-2meimao2", "bone": "bone", "attachment": "2-2meimao2"}, {"name": "2-2meimao1", "bone": "bone", "attachment": "2-2meimao1"}], "skins": [{"name": "default", "attachments": {"2-2meimao1": {"2-2meimao1": {"type": "mesh", "hull": 4, "width": 17, "height": 9, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 6, -13.59, 6.78, 1, 1, 6, -13.35, 23.78, 1, 1, 6, -4.35, 23.65, 1, 1, 6, -4.59, 6.66, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-2meimao2": {"2-2meimao2": {"type": "mesh", "hull": 4, "width": 16, "height": 9, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 6, -14.02, -24.22, 1, 1, 6, -13.8, -8.22, 1, 1, 6, -4.8, -8.34, 1, 1, 6, -5.02, -24.34, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-2shenti": {"2-2shenti": {"type": "mesh", "hull": 4, "width": 104, "height": 109, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -2.44, -51.48, 1, 1, 2, -2.79, 52.52, 1, 1, 2, 106.21, 52.89, 1, 1, 2, 106.56, -51.11, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-2tou": {"2-2tou": {"type": "mesh", "hull": 4, "width": 152, "height": 126, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -0.92, -75.21, 1, 1, 3, 1.19, 76.77, 1, 1, 3, 127.18, 75.02, 1, 1, 3, 125.06, -76.97, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-2yanjin1": {"2-2yanjin1": {"type": "mesh", "hull": 4, "width": 20, "height": 24, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 4, -10.31, 6.34, 1, 1, 4, -10.03, 26.34, 1, 1, 4, 13.97, 26, 1, 1, 4, 13.69, 6, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-2yanjin2": {"2-2yanjin2": {"type": "mesh", "hull": 4, "width": 18, "height": 39, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 4, -28.73, -23.41, 1, 1, 4, -28.48, -5.41, 1, 1, 4, 10.52, -5.95, 1, 1, 4, 10.27, -23.95, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"bones": {"bone3": {"translate": [{"x": 13.51, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 9.56, "y": 0.03, "curve": "stepped"}, {"time": 0.2333, "x": 9.56, "y": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 13.51, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 9.56, "y": 0.03, "curve": "stepped"}, {"time": 0.7333, "x": 9.56, "y": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 13.51, "y": 0.05}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 0.976, "curve": "stepped"}, {"time": 0.2333, "x": 0.976, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 0.976, "curve": "stepped"}, {"time": 0.7333, "x": 0.976, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "bone4": {"translate": [{"x": 0.52, "y": -0.01, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "x": 0.73, "y": -0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -2.63, "y": 0.04, "curve": "stepped"}, {"time": 0.2667, "x": -2.63, "y": 0.04, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "x": 0.52, "y": -0.01, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.5333, "x": 0.73, "y": -0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -2.63, "y": 0.04, "curve": "stepped"}, {"time": 0.7667, "x": -2.63, "y": 0.04, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1, "x": 0.52, "y": -0.01}]}, "bone6": {"translate": [{"x": 7.4, "y": -0.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "x": 8.44, "y": -0.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 2.8, "y": -0.04, "curve": "stepped"}, {"time": 0.3, "x": 2.8, "y": -0.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "x": 7.4, "y": -0.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5667, "x": 8.44, "y": -0.12, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 2.8, "y": -0.04, "curve": "stepped"}, {"time": 0.8, "x": 2.8, "y": -0.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1, "x": 7.4, "y": -0.1}]}, "bone5": {"translate": [{"x": -0.9, "y": 0.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -4.89, "y": 0.07, "curve": "stepped"}, {"time": 0.3, "x": -4.89, "y": 0.07, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "x": -0.9, "y": 0.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -4.89, "y": 0.07, "curve": "stepped"}, {"time": 0.8, "x": -4.89, "y": 0.07, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1, "x": -0.9, "y": 0.01}], "scale": [{"x": 1.158, "y": 0.934, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "y": 0.953, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 1.857, "y": 0.848, "curve": "stepped"}, {"time": 0.3, "x": 1.857, "y": 0.848, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "x": 1.158, "y": 0.934, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5667, "y": 0.953, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 1.857, "y": 0.848, "curve": "stepped"}, {"time": 0.8, "x": 1.857, "y": 0.848, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1, "x": 1.158, "y": 0.934}]}}}}}