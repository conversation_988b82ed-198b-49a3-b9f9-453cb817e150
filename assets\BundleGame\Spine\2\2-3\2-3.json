{"skeleton": {"hash": "lLV7stGERZpfTcdQ7AZBGfrquPo", "spine": "3.8.99", "x": -65.49, "y": -1.96, "width": 140.71, "height": 248.77, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/系列2/2-3"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": 0.81, "scaleX": 0.7606, "scaleY": 0.7606}, {"name": "bone2", "parent": "bone", "length": 149.98, "rotation": 90, "x": -0.82, "y": 0.25}, {"name": "bone3", "parent": "bone2", "length": 91.6, "x": 131.79, "y": 0.33}, {"name": "bone4", "parent": "bone3", "x": 96.78, "y": -15.65}, {"name": "bone9", "parent": "bone4", "length": 32.55, "rotation": 80.71, "x": -0.42, "y": -0.01}, {"name": "bone10", "parent": "bone9", "length": 37.63, "rotation": -12.1, "x": 28.74}, {"name": "bone5", "parent": "bone3", "length": 40.93, "rotation": 48.01, "x": 71.93, "y": 47.46}, {"name": "bone6", "parent": "bone3", "length": 42.45, "rotation": -52.77, "x": 71.93, "y": -50.14}, {"name": "bone7", "parent": "bone3", "x": 55.22, "y": -0.3}, {"name": "bone8", "parent": "bone3", "x": 24.18, "y": -0.87}, {"name": "bone11", "parent": "bone3", "x": 70.08, "y": 0.43}], "slots": [{"name": "2-3<PERSON><PERSON>", "bone": "bone", "attachment": "2-3<PERSON><PERSON>"}, {"name": "2-3<PERSON><PERSON>", "bone": "bone", "attachment": "2-3<PERSON><PERSON>"}, {"name": "2-3<PERSON><PERSON><PERSON>", "bone": "bone", "attachment": "2-3<PERSON><PERSON><PERSON>"}, {"name": "2-3zuiba", "bone": "bone", "attachment": "2-3zuiba"}, {"name": "2-3yanjin2", "bone": "bone", "attachment": "2-3yanjin2"}, {"name": "2-3yanjin1", "bone": "bone", "attachment": "2-3yanjin1"}, {"name": "2-3meimao2", "bone": "bone", "attachment": "2-3meimao2"}, {"name": "2-3meimao1", "bone": "bone", "attachment": "2-3meimao1"}], "skins": [{"name": "default", "attachments": {"2-3meimao1": {"2-3meimao1": {"type": "mesh", "hull": 4, "width": 18, "height": 13, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 11, -6.16, 16.59, 1, 1, 11, -6.16, 34.59, 1, 1, 11, 6.84, 34.59, 1, 1, 11, 6.84, 16.59, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-3meimao2": {"2-3meimao2": {"type": "mesh", "hull": 4, "width": 19, "height": 13, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 11, -6.16, -36.41, 1, 1, 11, -6.16, -17.41, 1, 1, 11, 6.84, -17.41, 1, 1, 11, 6.84, -36.41, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-3shenti": {"2-3shenti": {"type": "mesh", "hull": 4, "width": 125, "height": 170, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -2.82, -63.65, 1, 1, 2, -2.82, 61.35, 1, 1, 2, 167.18, 61.35, 1, 1, 2, 167.18, -63.65, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-3tou": {"2-3tou": {"type": "mesh", "hull": 25, "width": 185, "height": 115, "uvs": [0.92688, 0.05418, 0.95697, 0.20148, 1, 0.2099, 1, 0.34668, 0.93604, 0.41402, 0.85624, 0.5003, 0.88751, 0.66723, 0.83911, 0.86083, 0.68083, 1, 0.23351, 1, 0.10682, 0.86514, 0.06889, 0.62524, 0.10647, 0.49905, 0.05188, 0.45058, 0, 0.34958, 0, 0.22331, 0.02834, 0.08653, 0.09244, 0, 0.23859, 0, 0.31707, 0.09501, 0.40887, 0.04365, 0.55276, 0.04365, 0.64041, 0.09205, 0.70974, 0, 0.8484, 0, 0.16403, 0.39593, 0.26083, 0.18971, 0.7045, 0.19306, 0.79346, 0.4056], "triangles": [13, 14, 25, 25, 15, 16, 26, 17, 18, 17, 26, 16, 25, 14, 15, 3, 4, 1, 4, 28, 1, 1, 24, 0, 24, 1, 27, 24, 27, 23, 1, 2, 3, 26, 18, 19, 7, 28, 5, 27, 22, 23, 9, 10, 12, 8, 26, 27, 8, 28, 7, 27, 21, 22, 26, 21, 27, 20, 26, 19, 21, 26, 20, 8, 9, 25, 8, 27, 28, 7, 5, 6, 8, 25, 26, 9, 12, 25, 11, 12, 10, 5, 28, 4, 1, 28, 27, 12, 13, 25, 25, 16, 26], "vertices": [2, 3, 103.23, -85.45, 0.01188, 8, 47.05, 3.55, 0.98812, 2, 3, 86.29, -91.02, 0.0003, 8, 41.23, -13.3, 0.9997, 2, 3, 85.32, -98.98, 0.00014, 8, 46.98, -18.89, 0.99986, 2, 3, 69.59, -98.98, 0.11139, 8, 37.47, -31.41, 0.88861, 2, 3, 61.85, -87.15, 0.44473, 8, 23.36, -30.42, 0.55527, 1, 3, 51.92, -72.39, 1, 1, 3, 32.73, -78.17, 1, 1, 3, 10.46, -69.22, 1, 1, 3, -5.54, -39.93, 1, 1, 3, -5.54, 42.82, 1, 1, 3, 9.97, 66.26, 1, 1, 3, 37.55, 73.28, 1, 1, 3, 52.07, 66.32, 1, 2, 3, 57.64, 76.42, 0.44697, 7, 11.97, 30, 0.55303, 2, 3, 69.26, 86.02, 0.11363, 7, 26.88, 27.79, 0.88637, 2, 3, 83.78, 86.02, 0.00156, 7, 36.59, 16.99, 0.99844, 2, 3, 99.51, 80.78, 0.01319, 7, 43.21, 1.79, 0.98681, 2, 3, 109.46, 68.92, 0.13719, 7, 41.06, -13.54, 0.86281, 2, 3, 109.46, 41.88, 0.47022, 7, 20.96, -31.62, 0.52978, 1, 3, 98.53, 27.36, 1, 1, 3, 104.44, 10.38, 1, 1, 3, 104.44, -16.24, 1, 1, 3, 98.87, -32.46, 1, 2, 3, 109.46, -45.28, 0.46762, 8, 18.84, 32.82, 0.53238, 2, 3, 109.46, -70.93, 0.13458, 8, 39.26, 17.3, 0.86542, 1, 3, 63.93, 55.68, 1, 1, 3, 87.64, 37.77, 1, 1, 3, 87.26, -44.31, 1, 1, 3, 62.81, -60.77, 1], "edges": [24, 50, 50, 52, 52, 38, 38, 36, 34, 36, 34, 32, 32, 30, 28, 30, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 38, 40, 40, 42, 42, 44, 44, 54, 54, 56, 56, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 48, 46, 48, 46, 44, 10, 12, 12, 14, 16, 18, 14, 16]}}, "2-3toufa": {"2-3toufa": {"type": "mesh", "hull": 14, "width": 115, "height": 117, "uvs": [0.77652, 0.52802, 0.90173, 0.57479, 1, 0.6831, 1, 0.89725, 0.71641, 1, 0.44093, 1, 0.16295, 0.89725, 0, 0.7471, 0, 0.60034, 0.16295, 0.54865, 0.31321, 0.49942, 0.46347, 0.5388, 0.43592, 0, 0.77902, 0, 0.47144, 0.78863, 0.52389, 0.84614, 0.63687, 0.86002, 0.78615, 0.82036, 0.7216, 0.71725], "triangles": [7, 8, 9, 7, 9, 6, 4, 5, 16, 14, 6, 9, 4, 17, 3, 9, 10, 14, 17, 2, 3, 5, 14, 15, 4, 16, 17, 5, 15, 16, 18, 11, 0, 18, 0, 1, 14, 11, 18, 17, 18, 1, 17, 1, 2, 15, 14, 18, 16, 15, 18, 17, 16, 18, 13, 11, 12, 13, 0, 11, 14, 10, 11, 5, 6, 14], "vertices": [1, 4, 33.89, -4.63, 1, 3, 4, 28.42, -19.03, 0.95925, 5, -14.11, -31.53, 0.0315, 6, -35.29, -39.81, 0.00926, 3, 4, 15.75, -30.33, 0.64814, 5, -27.31, -20.85, 0.31904, 6, -50.44, -32.14, 0.03282, 3, 4, -9.31, -30.33, 0.38688, 5, -31.36, 3.87, 0.60578, 6, -59.57, -8.81, 0.00734, 3, 4, -21.33, 2.28, 0.19823, 5, -1.11, 21, 0.80015, 6, -33.59, 14.28, 0.00162, 3, 4, -21.33, 33.96, 0.176, 5, 30.15, 26.12, 0.61237, 6, -4.09, 25.83, 0.21163, 3, 4, -9.31, 65.93, 0.176, 5, 63.64, 19.42, 0.34025, 6, 30.06, 26.3, 0.48375, 3, 4, 8.26, 84.67, 0.176, 5, 84.97, 5.11, 0.06721, 6, 53.91, 16.78, 0.75679, 3, 4, 25.43, 84.67, 0.17646, 5, 87.74, -11.84, 0.00254, 6, 60.17, 0.79, 0.821, 2, 4, 31.48, 65.93, 0.19211, 6, 44.93, -11.68, 0.80789, 2, 4, 37.24, 48.65, 0.52544, 6, 30.94, -23.34, 0.47456, 1, 4, 32.63, 31.37, 1, 1, 4, 95.67, 34.54, 1, 1, 4, 95.67, -4.92, 1, 1, 4, 3.4, 30.45, 1, 1, 4, -3.32, 24.42, 1, 1, 4, -4.95, 11.43, 1, 1, 4, -0.31, -5.74, 1, 1, 4, 11.76, 1.68, 1], "edges": [16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 0, 0, 2, 2, 4, 14, 16, 14, 12, 12, 10, 8, 10, 4, 6, 8, 6, 22, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 0]}}, "2-3yanjin1": {"2-3yanjin1": {"type": "mesh", "hull": 4, "width": 20, "height": 20, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 9, -13.76, 15.32, 1, 1, 9, -13.76, 35.32, 1, 1, 9, 6.24, 35.32, 1, 1, 9, 6.24, 15.32, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-3yanjin2": {"2-3yanjin2": {"type": "mesh", "hull": 4, "width": 19, "height": 20, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 9, -13.76, -34.68, 1, 1, 9, -13.76, -15.68, 1, 1, 9, 6.24, -15.68, 1, 1, 9, 6.24, -34.68, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-3zuiba": {"2-3zuiba": {"type": "mesh", "hull": 12, "width": 56, "height": 26, "uvs": [0.85244, 0.25004, 1, 0.6285, 1, 1, 0.75287, 1, 0.56251, 0.76727, 0.39266, 0.74834, 0.23744, 1, 0, 1, 0, 0.44558, 0.13202, 0.22481, 0.32823, 0, 0.64159, 0], "triangles": [5, 10, 11, 9, 10, 5, 4, 5, 11, 4, 11, 0, 8, 6, 7, 9, 6, 8, 9, 5, 6, 3, 4, 0, 3, 0, 1, 3, 1, 2], "vertices": [1, 10, 2.78, -19.85, 1, 1, 10, -7.06, -28.11, 1, 1, 10, -16.72, -28.11, 1, 1, 10, -16.72, -14.27, 1, 1, 10, -10.67, -3.61, 1, 1, 10, -10.17, 5.9, 1, 1, 10, -16.72, 14.59, 1, 1, 10, -16.72, 27.89, 1, 1, 10, -2.3, 27.89, 1, 1, 10, 3.44, 20.49, 1, 1, 10, 9.28, 9.51, 1, 1, 10, 9.28, -8.04, 1], "edges": [14, 16, 16, 18, 18, 20, 20, 22, 22, 0, 4, 2, 0, 2, 4, 6, 6, 8, 8, 10, 12, 14, 10, 12]}}}}], "animations": {"animation": {"bones": {"bone3": {"rotate": [{"angle": 2.39, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 7.66, "curve": "stepped"}, {"time": 0.2333, "angle": 7.66, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 7.66, "curve": "stepped"}, {"time": 0.7333, "angle": 7.66, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 2.39}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -4.39, "y": 8.78, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -2, "y": 4.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -4.39, "y": 8.78, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -2, "y": 4.39, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "bone7": {"translate": [{"x": 1.96, "y": -0.19, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "x": 2.38, "y": -0.24, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -4.35, "y": 0.58, "curve": "stepped"}, {"time": 0.2667, "x": -4.35, "y": 0.58, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "x": 1.96, "y": -0.19, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.5333, "x": 2.38, "y": -0.24, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -4.35, "y": 0.58, "curve": "stepped"}, {"time": 0.7667, "x": -4.35, "y": 0.58, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1, "x": 1.96, "y": -0.19}]}, "bone8": {"translate": [{"x": -0.07, "y": 0.01, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -1.19, "y": 0.16, "curve": "stepped"}, {"time": 0.2667, "x": -1.19, "y": 0.16, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "x": -0.07, "y": 0.01, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -1.19, "y": 0.16, "curve": "stepped"}, {"time": 0.7667, "x": -1.19, "y": 0.16, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1, "x": -0.07, "y": 0.01}], "scale": [{"x": 1.016, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 1.262, "curve": "stepped"}, {"time": 0.2667, "x": 1.262, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "x": 1.016, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.262, "curve": "stepped"}, {"time": 0.7667, "x": 1.262, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1, "x": 1.016}]}, "bone5": {"rotate": [{"angle": -5.94, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": -8.76, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 12.94, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.5, "angle": -5.94, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5667, "angle": -8.76, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 12.94, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": -5.94}]}, "bone6": {"rotate": [{"angle": 6.76, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": 9.36, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -10.63, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.5, "angle": 6.76, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5667, "angle": 9.36, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -10.63, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": 6.76}]}, "bone4": {"rotate": [{"angle": -10.02, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -6.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 14.34, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -16.68, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5, "angle": -10.02, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5667, "angle": -6.14, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 14.34, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -16.68, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": -10.02}], "translate": [{"x": -0.6, "y": -1.68, "curve": "stepped"}, {"time": 0.0667, "x": -0.6, "y": -1.68, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -0.2, "y": 14.84, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -0.6, "y": -1.68, "curve": "stepped"}, {"time": 0.5667, "x": -0.6, "y": -1.68, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -0.2, "y": 14.84, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -0.6, "y": -1.68}]}, "bone9": {"rotate": [{"angle": 1.31, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1, "angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 10.37, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5, "angle": 1.31, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.6, "angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 10.37, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 1, "angle": 1.31}]}, "bone10": {"rotate": [{"angle": 3.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 10.37, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": 3.55, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6333, "angle": -3.27, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 10.37, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": 3.55}]}}}}}