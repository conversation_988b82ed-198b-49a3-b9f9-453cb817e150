{"skeleton": {"hash": "uw4DycB3n9r2R+K2aOFH0UcfaBM", "spine": "3.8.99", "x": -61.65, "y": -1.65, "width": 127, "height": 206, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/系列2/2-14"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "bone2", "parent": "bone", "x": 0.64, "y": 70.73}, {"name": "bone3", "parent": "bone", "x": 0.64, "y": 51.16}], "slots": [{"name": "2-14<PERSON><PERSON>", "bone": "bone", "attachment": "2-14<PERSON><PERSON>"}, {"name": "2-14zu<PERSON>", "bone": "bone", "attachment": "2-14zu<PERSON>"}, {"name": "2-14<PERSON><PERSON>1", "bone": "bone", "attachment": "2-14<PERSON><PERSON>1"}], "skins": [{"name": "default", "attachments": {"2-14shenti": {"2-14shenti": {"type": "mesh", "hull": 4, "width": 127, "height": 206, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [65.35, -1.65, -61.65, -1.65, -61.65, 204.35, 65.35, 204.35], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-14yanjin1": {"2-14yanjin1": {"type": "mesh", "hull": 4, "width": 43, "height": 25, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, 21.71, -12.38, 1, 1, 2, -21.29, -12.38, 1, 1, 2, -21.29, 12.62, 1, 1, 2, 21.71, 12.62, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-14zuiba": {"2-14zuiba": {"type": "mesh", "hull": 4, "width": 29, "height": 23, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, 13.71, -13.81, 1, 1, 3, -15.29, -13.81, 1, 1, 3, -15.29, 9.19, 1, 1, 3, 13.71, 9.19, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"bones": {"bone2": {"scale": [{"y": 0.903, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 1.236, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": 0.903}]}, "bone3": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": -2.52, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}], "scale": [{"y": 0.903, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 1.236, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": 0.903}]}}}}}