{"skeleton": {"hash": "pCp5s9TeT5kTvvItAuRnKKRDXPA", "spine": "3.8.99", "x": -56.23, "y": -1.85, "width": 113.82, "height": 204.7, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/系列2/2-11"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "scaleX": 0.9179, "scaleY": 0.9179}, {"name": "bone2", "parent": "bone", "length": 102.54, "rotation": 89.8, "x": -0.38, "y": 0.56}, {"name": "bone2b", "parent": "bone2", "length": 102.54, "x": 107.6}, {"name": "bone2b2", "parent": "bone2b", "x": 51.41, "y": 30.04}, {"name": "bone2b3", "parent": "bone2b", "x": 51.61, "y": -28.55}], "slots": [{"name": "2-11<PERSON><PERSON>", "bone": "bone", "attachment": "2-11<PERSON><PERSON>"}, {"name": "2-11<PERSON><PERSON>", "bone": "bone", "attachment": "2-11<PERSON><PERSON>"}, {"name": "2-11<PERSON><PERSON>", "bone": "bone", "attachment": "2-11<PERSON><PERSON>"}], "skins": [{"name": "default", "attachments": {"2-11shenti": {"2-11shenti": {"type": "mesh", "hull": 4, "width": 89, "height": 132, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -2.41, -46.8, 1, 1, 2, -2.71, 42.2, 1, 1, 2, 129.29, 42.65, 1, 1, 2, 129.59, -46.35, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-11tou": {"2-11tou": {"type": "mesh", "hull": 4, "width": 124, "height": 124, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -10.95, -62.78, 1, 1, 3, -11.38, 61.21, 1, 1, 3, 112.62, 61.64, 1, 1, 3, 113.05, -62.36, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-11yanjin": {"2-11yanjin": {"type": "mesh", "hull": 24, "width": 41, "height": 59, "uvs": [0.37778, 0.05684, 0.45197, 0.16525, 0.59085, 0.26043, 0.68597, 0.40982, 0.73733, 0.53409, 0.74685, 0.60813, 0.90762, 0.64684, 1, 0.74864, 1, 0.85308, 0.89621, 0.96016, 0.63367, 1, 0.45485, 0.95884, 0.36543, 0.81214, 0.42631, 0.68919, 0.53187, 0.64382, 0.46719, 0.55789, 0.36826, 0.4746, 0.29978, 0.42127, 0.22178, 0.43977, 0.10383, 0.40672, 0, 0.29171, 0, 0.16525, 0.07148, 0.04759, 0.2389, 0, 0.3968, 0.31728, 0.62699, 0.61341], "triangles": [24, 20, 21, 1, 22, 0, 24, 1, 2, 24, 21, 22, 0, 22, 23, 1, 24, 22, 19, 20, 24, 17, 19, 24, 18, 19, 17, 16, 17, 24, 24, 3, 16, 3, 24, 2, 15, 3, 4, 15, 16, 3, 25, 15, 4, 5, 25, 4, 14, 15, 25, 7, 5, 6, 14, 12, 13, 10, 11, 12, 7, 9, 5, 8, 9, 7, 10, 25, 5, 10, 5, 9, 14, 25, 10, 10, 12, 14], "vertices": [1, 5, 14.99, -6.64, 1, 1, 5, 8.6, -9.7, 1, 1, 5, 3.01, -15.42, 1, 1, 5, -5.79, -19.35, 1, 1, 5, -13.12, -21.48, 1, 1, 5, -17.49, -21.88, 1, 1, 5, -19.75, -28.48, 1, 1, 5, -25.74, -32.29, 1, 1, 5, -31.9, -32.31, 1, 1, 5, -38.23, -28.08, 1, 1, 5, -40.62, -17.32, 1, 1, 5, -38.22, -9.98, 1, 1, 5, -29.58, -6.29, 1, 1, 5, -22.31, -8.76, 1, 1, 5, -19.62, -13.08, 1, 1, 5, -14.56, -10.41, 1, 1, 5, -9.66, -6.33, 1, 1, 5, -6.52, -3.52, 1, 1, 5, -7.63, -0.32, 1, 1, 5, -5.69, 4.52, 1, 1, 5, 1.08, 8.8, 1, 1, 5, 8.54, 8.83, 1, 1, 5, 15.49, 5.92, 1, 1, 5, 18.32, -0.93, 1, 1, 5, -0.37, -7.47, 1, 1, 5, -17.81, -16.97, 1], "edges": [40, 38, 38, 36, 36, 34, 34, 48, 48, 2, 2, 0, 0, 46, 46, 44, 40, 42, 44, 42, 2, 4, 4, 6, 6, 8, 8, 10, 10, 50, 50, 28, 28, 30, 30, 32, 32, 34, 28, 26, 26, 24, 24, 22, 22, 20, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20]}}}}], "animations": {"animation": {"bones": {"bone2b": {"rotate": [{"angle": 13.42, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -8.75, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 13.42}], "translate": [{"x": 1.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -2.36, "y": -0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 1.18, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": -2.36, "y": -0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 1.18}]}, "bone2b3": {"translate": [{"x": -0.54, "y": 8.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -1.16, "y": -7.03, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -0.54, "y": 8.01}]}}}}}