{"skeleton": {"hash": "SvHFA85cQww7gckMS7AFj+z40IU", "spine": "3.8.99", "x": -73.57, "y": -2.84, "width": 152, "height": 212, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/1-4"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "bone2", "parent": "bone", "length": 94.6, "rotation": 90, "x": 0.37, "y": 0.22}, {"name": "bone3", "parent": "bone2", "length": 59.81, "x": 94.15, "y": 0.21}, {"name": "bone4", "parent": "bone3", "x": 77.24, "y": 17.14}, {"name": "bone5", "parent": "bone4", "length": 27.55, "rotation": 46.94}, {"name": "bone6", "parent": "bone5", "length": 27.36, "rotation": 59.53, "x": 27.55}, {"name": "bone7", "parent": "bone3", "x": 76.99, "y": -22.29}, {"name": "bone8", "parent": "bone7", "length": 25.8, "rotation": -43.19}, {"name": "bone9", "parent": "bone8", "length": 33.23, "rotation": -68.07, "x": 25.8}, {"name": "bone10", "parent": "bone3", "x": 38.54, "y": -0.45}, {"name": "bone11", "parent": "bone3", "x": 17.39, "y": -0.08}, {"name": "bone12", "parent": "bone11", "length": 10.27, "rotation": 179.4, "x": -3.37, "y": 8.8}], "slots": [{"name": "1-4<PERSON><PERSON>2", "bone": "bone", "attachment": "1-4<PERSON><PERSON>2"}, {"name": "1-4<PERSON>uo1", "bone": "bone", "attachment": "1-4<PERSON>uo1"}, {"name": "1-4<PERSON><PERSON>", "bone": "bone", "attachment": "1-4<PERSON><PERSON>"}, {"name": "1-4<PERSON><PERSON>", "bone": "bone", "attachment": "1-4<PERSON><PERSON>"}, {"name": "1-4zuiba", "bone": "bone", "attachment": "1-4zuiba"}, {"name": "1-4yanjing1", "bone": "bone", "attachment": "1-4yanjing2"}], "skins": [{"name": "default", "attachments": {"1-4erduo1": {"1-4erduo1": {"type": "mesh", "hull": 14, "width": 63, "height": 44, "uvs": [0.84418, 0.16337, 1, 0.43835, 1, 0.75048, 0.90992, 0.91893, 0.7196, 0.92389, 0.52929, 0.82232, 0.3978, 0.83223, 0.27842, 1, 0, 1, 0, 0.57249, 0.0581, 0.30742, 0.21036, 0.08694, 0.41798, 0, 0.65732, 0], "triangles": [4, 5, 0, 5, 12, 13, 5, 13, 0, 0, 1, 4, 6, 12, 5, 1, 2, 4, 3, 4, 2, 6, 11, 12, 6, 10, 11, 6, 9, 10, 7, 8, 9, 6, 7, 9], "vertices": [1, 9, 23.38, 18.54, 1, 1, 9, 36.91, 10.82, 1, 1, 9, 41.89, -1.98, 1, 1, 9, 39.29, -10.94, 1, 2, 8, 21.96, -31.94, 0.01506, 9, 28.19, -15.49, 0.98494, 2, 8, 17.01, -20.14, 0.25504, 9, 15.4, -15.67, 0.74496, 2, 8, 11.02, -14.4, 0.72428, 9, 7.84, -19.08, 0.27572, 2, 8, 0.49, -13.96, 0.97123, 9, 3.5, -28.69, 0.02877, 1, 8, -11.51, -1.18, 1, 1, 8, 2.2, 11.7, 1, 2, 8, 13.21, 17.01, 0.99979, 9, -20.48, -5.32, 0.00021, 2, 8, 26.85, 16.66, 0.79774, 9, -15.06, 7.2, 0.20226, 2, 8, 38.59, 9.74, 0.1492, 9, -4.25, 15.51, 0.8508, 1, 9, 9.8, 20.97, 1], "edges": [16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 14, 16, 12, 14]}}, "1-4erduo2": {"1-4erduo2": {"type": "mesh", "hull": 16, "width": 63, "height": 46, "uvs": [0.80055, 0.12649, 0.9462, 0.33002, 1, 0.57128, 1, 0.81933, 0.86881, 1, 0.66214, 0.93255, 0.58341, 0.7735, 0.40922, 0.78968, 0.21564, 0.90167, 0.04637, 0.82619, 0, 0.65367, 0, 0.45257, 0.08255, 0.22748, 0.2528, 0.07383, 0.44763, 0, 0.62833, 0], "triangles": [11, 7, 10, 6, 14, 15, 7, 13, 14, 7, 12, 13, 12, 7, 11, 6, 7, 14, 8, 9, 10, 7, 8, 10, 6, 15, 0, 6, 0, 1, 6, 1, 2, 5, 6, 2, 3, 5, 2, 4, 5, 3], "vertices": [2, 5, 25.48, -18.26, 0.7809, 6, -16.79, -7.47, 0.2191, 2, 5, 12.38, -17.68, 0.99592, 6, -22.93, 4.11, 0.00408, 1, 5, 2.33, -11.89, 1, 1, 5, -5.46, -3.55, 1, 1, 5, -5.1, 8.16, 1, 2, 5, 6.53, 14.79, 0.95874, 6, 2.08, 25.61, 0.04126, 2, 5, 15.15, 12.83, 0.709, 6, 4.77, 17.19, 0.291, 2, 5, 22.66, 20.86, 0.12747, 6, 15.5, 14.79, 0.87253, 2, 5, 28.06, 32.95, 0.0014, 6, 28.66, 16.28, 0.9986, 1, 6, 37.9, 9.92, 1, 1, 6, 38.45, 1.49, 1, 1, 6, 35.83, -7.39, 1, 1, 6, 27.91, -15.84, 1, 1, 6, 15.62, -19.58, 1, 2, 5, 45.69, -7.33, 0.02576, 6, 2.88, -19.36, 0.97424, 2, 5, 37.38, -15.1, 0.28773, 6, -8.03, -16.13, 0.71227], "edges": [22, 24, 24, 26, 26, 28, 28, 30, 30, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 20, 22, 18, 20]}}, "1-4shenti": {"1-4shenti": {"type": "mesh", "hull": 4, "width": 86, "height": 121, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -3.06, -44.06, 1, 1, 2, -3.06, 41.94, 1, 1, 2, 117.94, 41.94, 1, 1, 2, 117.94, -44.06, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-4tou": {"1-4tou": {"type": "mesh", "hull": 4, "width": 105, "height": 117, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -2.21, -57.27, 1, 1, 3, -2.21, 47.73, 1, 1, 3, 114.79, 47.73, 1, 1, 3, 114.79, -57.27, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-4yanjing1": {"1-4yanjing1": {"type": "mesh", "hull": 8, "width": 72, "height": 12, "uvs": [1, 1, 0.70006, 1, 0.30651, 1, 0, 1, 0, 0, 0.29884, 0, 0.70006, 0, 1, 0], "triangles": [3, 4, 5, 2, 5, 6, 3, 5, 2, 1, 2, 6, 6, 7, 0, 1, 6, 0], "vertices": [1, 10, -6.74, -34.62, 1, 1, 10, -6.74, -13.03, 1, 1, 10, -6.74, 13.1, 1, 1, 10, -6.74, 35.17, 1, 1, 10, 5.26, 35.17, 1, 1, 10, 5.26, 13.65, 1, 1, 10, 5.26, -13.03, 1, 1, 10, 5.26, -34.62, 1], "edges": [6, 8, 0, 14, 8, 10, 4, 6, 10, 4, 10, 12, 12, 14, 0, 2, 2, 4, 12, 2]}, "1-4yanjing2": {"type": "mesh", "hull": 8, "width": 75, "height": 18, "uvs": [1, 1, 0.70257, 1, 0.29286, 1, 0, 1, 0, 0, 0.28796, 0, 0.70012, 0, 1, 0], "triangles": [3, 4, 5, 2, 5, 6, 3, 5, 2, 1, 6, 7, 2, 6, 1, 1, 7, 0], "vertices": [1, 10, -6.74, -35.33, 1, 1, 10, -6.74, -13.03, 1, 1, 10, -6.74, 14.21, 1, 1, 10, -6.74, 36.17, 1, 1, 10, 11.26, 36.17, 1, 1, 10, 11.26, 14.57, 1, 1, 10, 11.26, -12.84, 1, 1, 10, 11.26, -35.33, 1], "edges": [6, 8, 0, 14, 12, 14, 0, 2, 12, 2, 8, 10, 10, 12, 2, 4, 4, 6, 10, 4]}}, "1-4zuiba": {"1-4zuiba": {"type": "mesh", "hull": 12, "width": 50, "height": 26, "uvs": [1, 0.42662, 0.77719, 0.65416, 0.45751, 0.63258, 0.46057, 0.74439, 0.47791, 0.86012, 0.37184, 1, 0.21782, 0.95623, 0.14234, 0.79539, 0.13928, 0.58747, 0, 0.30893, 0, 0, 1, 0, 0.16274, 0.43839, 0.45445, 0.44231], "triangles": [6, 2, 3, 5, 6, 3, 6, 7, 2, 4, 5, 3, 11, 12, 10, 2, 12, 13, 11, 13, 12, 11, 1, 13, 1, 2, 13, 11, 0, 1, 12, 9, 10, 8, 9, 12, 2, 7, 8, 8, 12, 2], "vertices": [1, 11, -1.69, -26.19, 1, 1, 11, -7.6, -15.05, 1, 1, 11, -7.04, 0.93, 1, 2, 11, -9.95, 0.78, 0.42349, 12, 6.49, 8.09, 0.57651, 2, 11, -12.96, -0.09, 0.09383, 12, 9.49, 8.99, 0.90617, 2, 11, -16.59, 5.21, 0.0331, 12, 13.18, 3.73, 0.9669, 2, 11, -15.46, 12.91, 0.00368, 12, 12.13, -3.99, 0.99632, 2, 11, -11.27, 16.69, 0.33333, 12, 7.98, -7.8, 0.66667, 1, 11, -5.87, 16.84, 1, 1, 11, 1.37, 23.81, 1, 1, 11, 9.41, 23.81, 1, 1, 11, 9.41, -26.19, 1, 1, 11, -1.99, 15.67, 1, 1, 11, -2.09, 1.08, 1], "edges": [20, 22, 18, 20, 16, 18, 16, 24, 24, 26, 26, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 4, 2, 0, 22, 2, 0]}}}}], "animations": {"animation": {"slots": {"1-4yanjing1": {"attachment": [{"time": 0.1667, "name": "1-4yanjing1"}, {"time": 0.8, "name": "1-4yanjing2"}]}}, "bones": {"bone3": {"translate": [{"x": -3.54, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "x": -3.48, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 0.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -2.29, "y": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -0.83, "y": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -4.06, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 1, "x": -3.54}]}, "bone10": {"translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "y": 3.19, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "y": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}]}, "bone12": {"rotate": [{"angle": -3.78, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -24.68, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 22.63, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -13.3, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": -3.78}], "translate": [{"x": -0.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 2.11, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": -2.34, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "x": -0.66}]}, "bone5": {"rotate": [{"angle": -2.27, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 3.75, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -12.38, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 16.72, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -7.99, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": -2.27}]}, "bone6": {"rotate": [{"angle": -4, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.75, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -12.38, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 16.72, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -7.99, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": -4}]}, "bone8": {"rotate": [{"angle": -2.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -2.96, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -11.65, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 17.09, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -7.44, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": -2.11}]}, "bone9": {"rotate": [{"angle": -3.72, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -2.96, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -11.65, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 17.09, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -7.44, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": -3.72}]}}}}}