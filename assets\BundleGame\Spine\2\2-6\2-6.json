{"skeleton": {"hash": "D9wGzyFoFi6foLiKy4iv9EI4DxA", "spine": "3.8.99", "x": -82.6, "y": -5.73, "width": 164.56, "height": 244.82, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/系列2/2-6"}, "bones": [{"name": "root"}, {"name": "b", "parent": "root", "scaleX": 0.9245, "scaleY": 0.9245}, {"name": "bone", "parent": "root"}, {"name": "bone2", "parent": "bone", "length": 88.55, "rotation": 90, "x": -0.58, "y": -0.07}, {"name": "bone3", "parent": "bone2", "length": 68.26, "x": 104.51, "y": 0.65}, {"name": "bone4", "parent": "bone3", "length": 38.21, "rotation": 71.71, "x": 77.44, "y": 40.5}, {"name": "bone5", "parent": "bone3", "length": 37.25, "rotation": -73.21, "x": 80.35, "y": -44.71}, {"name": "bone6", "parent": "bone3", "x": 53.54, "y": -1.87}, {"name": "bone10", "parent": "bone6", "x": 6.86, "y": 25.46}, {"name": "bone7", "parent": "bone3", "x": 7.8, "y": -2.67}, {"name": "bone8", "parent": "bone7", "length": 8.17, "rotation": 179.24, "x": -7.25, "y": -9.85}, {"name": "bone9", "parent": "bone3", "x": 71.34, "y": -0.62}], "slots": [{"name": "2-6<PERSON><PERSON>", "bone": "b", "attachment": "2-6<PERSON><PERSON>"}, {"name": "2-6tou1", "bone": "b", "attachment": "2-6tou1"}, {"name": "2-6yanjin2", "bone": "b", "attachment": "2-6yanjin2"}, {"name": "2-6yanjin1", "bone": "b", "attachment": "2-6yanjin1"}, {"name": "2-6meimao2", "bone": "b", "attachment": "2-6meimao2"}, {"name": "2-6meimao1", "bone": "b", "attachment": "2-6meimao1"}], "skins": [{"name": "default", "attachments": {"2-6meimao1": {"2-6meimao1": {"type": "mesh", "hull": 4, "width": 21, "height": 17, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 11, -6.04, 5.26, 1, 1, 11, -6.04, 24.67, 1, 1, 11, 9.68, 24.67, 1, 1, 11, 9.68, 5.26, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-6meimao2": {"2-6meimao2": {"type": "mesh", "hull": 4, "width": 22, "height": 16, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 11, -4.19, -31.73, 1, 1, 11, -4.19, -11.39, 1, 1, 11, 10.61, -11.39, 1, 1, 11, 10.61, -31.73, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-6shenti": {"2-6shenti": {"type": "mesh", "hull": 4, "width": 149, "height": 172, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -5.67, -69.6, 1, 1, 3, -5.67, 68.16, 1, 1, 3, 153.35, 68.16, 1, 1, 3, 153.35, -69.6, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-6tou1": {"2-6tou1": {"type": "mesh", "hull": 33, "width": 178, "height": 165, "uvs": [0.53066, 0.14341, 0.70266, 0.20841, 0.79063, 0.21381, 0.88156, 0.21263, 0.98016, 0.22091, 0.96591, 0.32609, 0.93634, 0.41236, 0.8958, 0.49981, 1, 0.57545, 0.93792, 0.77211, 0.87095, 0.82449, 0.77552, 0.81185, 0.60476, 0.9166, 0.5921, 0.912, 0.59277, 0.95848, 0.59078, 1, 0.55299, 1, 0.5519, 0.95809, 0.551, 0.92344, 0.50488, 0.93539, 0.40043, 0.90614, 0.1226, 0.79198, 0.03555, 0.56983, 0.15364, 0.47944, 0.07476, 0.41917, 0, 0.34117, 0, 0.21826, 0.1131, 0.21708, 0.22265, 0.21708, 0.3333, 0.22417, 0.28619, 0.07644, 0.36914, 0, 0.50985, 0, 0.25442, 0.30453, 0.18979, 0.41326, 0.77058, 0.29823, 0.8476, 0.40763, 0.52979, 0.86265, 0.55631, 0.84406, 0.59144, 0.84263, 0.60006, 0.85479, 0.54835, 0.87839], "triangles": [15, 16, 17, 14, 15, 17, 5, 3, 4, 36, 3, 5, 25, 26, 27, 24, 25, 27, 35, 1, 2, 36, 35, 3, 7, 36, 6, 33, 28, 29, 33, 34, 27, 23, 24, 34, 9, 7, 8, 36, 39, 35, 11, 36, 7, 11, 7, 9, 35, 0, 1, 29, 30, 31, 21, 22, 23, 33, 29, 20, 34, 33, 20, 23, 34, 20, 10, 11, 9, 0, 35, 38, 11, 39, 36, 39, 38, 35, 40, 39, 11, 32, 0, 31, 38, 20, 0, 41, 37, 38, 31, 0, 29, 38, 37, 20, 20, 29, 0, 21, 23, 20, 40, 38, 39, 40, 41, 38, 13, 41, 40, 12, 40, 11, 13, 40, 12, 18, 41, 13, 19, 20, 37, 19, 37, 41, 19, 41, 18, 14, 18, 13, 33, 27, 28, 34, 24, 27, 3, 35, 2, 6, 36, 5, 14, 17, 18], "vertices": [1, 4, 112.77, -5.95, 1, 1, 4, 102.85, -34.26, 1, 3, 4, 102.03, -48.73, 0.44822, 5, -77.01, -51.35, 0.00025, 6, 10.11, 19.59, 0.55153, 3, 4, 102.21, -63.7, 0.1149, 5, -91.16, -56.22, 0.00025, 6, 24.49, 15.44, 0.88485, 3, 4, 100.95, -79.92, 0.00422, 5, -106.96, -60.11, 0.00013, 6, 39.66, 9.55, 0.99566, 2, 4, 84.9, -77.58, 0.11576, 6, 32.78, -5.14, 0.88424, 2, 4, 71.74, -72.71, 0.44908, 6, 24.32, -16.33, 0.55092, 1, 4, 58.4, -66.04, 1, 1, 4, 46.86, -83.19, 1, 1, 4, 16.86, -72.97, 1, 1, 4, 8.87, -61.95, 1, 1, 4, 10.8, -46.25, 1, 1, 4, -5.18, -18.15, 1, 1, 4, -4.48, -16.06, 1, 2, 4, -11.57, -16.17, 0.33333, 10, 12.07, 3.81, 0.66667, 1, 10, 18.4, 3.57, 1, 1, 10, 18.49, -2.65, 1, 2, 4, -11.51, -9.45, 0.33333, 10, 12.1, -2.91, 0.66667, 1, 4, -6.22, -9.3, 1, 1, 4, -8.04, -1.71, 1, 1, 4, -3.58, 15.48, 1, 1, 4, 13.83, 61.2, 1, 1, 4, 47.72, 75.53, 1, 1, 4, 61.51, 56.09, 1, 2, 4, 70.7, 69.07, 0.44509, 5, 25.02, 15.36, 0.55491, 2, 4, 82.6, 81.38, 0.11175, 5, 40.43, 7.92, 0.88825, 3, 4, 101.35, 81.38, 0.00352, 5, 46.32, -9.88, 0.99647, 6, -114.65, 56.53, 1e-05, 3, 4, 101.53, 62.76, 0.11751, 5, 28.7, -15.89, 0.88246, 6, -96.78, 51.32, 3e-05, 3, 4, 101.53, 44.73, 0.45084, 5, 11.58, -21.55, 0.54913, 6, -79.52, 46.11, 3e-05, 1, 4, 100.45, 26.53, 1, 1, 4, 122.99, 34.28, 1, 1, 4, 134.65, 20.63, 1, 1, 4, 134.65, -2.53, 1, 1, 4, 88.19, 39.51, 1, 1, 4, 71.61, 50.14, 1, 1, 4, 89.15, -45.43, 1, 1, 4, 72.46, -58.11, 1, 1, 4, 3.05, -5.81, 1, 1, 4, 5.89, -10.17, 1, 1, 4, 6.11, -15.95, 1, 1, 4, 4.25, -17.37, 1, 1, 4, 0.65, -8.86, 1], "edges": [58, 66, 66, 68, 68, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 0, 0, 2, 2, 70, 70, 72, 72, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 14, 16, 46, 44, 44, 42, 42, 40, 40, 38, 24, 22, 22, 20, 20, 18, 18, 16, 74, 76, 76, 78, 78, 80, 74, 82, 82, 36, 36, 38, 80, 26, 26, 28, 30, 32, 28, 30, 24, 26, 32, 34, 34, 36]}}, "2-6yanjin1": {"2-6yanjin1": {"type": "mesh", "hull": 18, "width": 55, "height": 48, "uvs": [1, 0.12652, 1, 0.3459, 0.9393, 0.48933, 0.84788, 0.60084, 0.78712, 0.67496, 0.75275, 0.89996, 0.57112, 1, 0.32076, 0.93089, 0.2643, 0.7734, 0, 0.7734, 0, 0.59621, 0.27903, 0.53152, 0.3723, 0.37965, 0.47539, 0.34308, 0.50485, 0.26433, 0.55394, 0.08433, 0.66439, 0, 0.81412, 0, 0.62757, 0.39652, 0.74539, 0.49496], "triangles": [17, 0, 1, 18, 15, 16, 14, 15, 18, 13, 14, 18, 17, 1, 18, 17, 18, 16, 2, 19, 1, 1, 19, 18, 3, 19, 2, 4, 19, 3, 8, 10, 11, 9, 10, 8, 19, 4, 18, 11, 18, 8, 7, 8, 6, 11, 12, 13, 11, 13, 18, 8, 4, 6, 4, 8, 18, 5, 6, 4], "vertices": [1, 8, -2.55, -15.25, 1, 1, 8, -12.28, -15.25, 1, 1, 8, -18.65, -12.16, 1, 1, 8, -23.6, -7.52, 1, 1, 8, -26.89, -4.43, 1, 1, 8, -36.87, -2.68, 1, 1, 8, -41.31, 6.56, 1, 1, 8, -38.24, 19.29, 1, 1, 8, -31.25, 22.16, 1, 1, 8, -31.25, 35.6, 1, 1, 8, -23.39, 35.6, 1, 1, 8, -20.52, 21.41, 1, 1, 8, -13.78, 16.67, 1, 1, 8, -12.16, 11.42, 1, 1, 8, -8.66, 9.93, 1, 1, 8, -0.68, 7.43, 1, 1, 8, 3.07, 1.81, 1, 1, 8, 3.07, -5.8, 1, 1, 8, -14.53, 3.69, 1, 1, 8, -18.9, -2.3, 1], "edges": [20, 22, 22, 24, 24, 26, 26, 36, 36, 38, 38, 8, 8, 10, 10, 12, 26, 28, 28, 30, 30, 32, 32, 34, 34, 0, 0, 2, 2, 4, 8, 6, 6, 4, 18, 20, 18, 16, 16, 14, 14, 12, 22, 16]}}, "2-6yanjin2": {"2-6yanjin2": {"type": "mesh", "hull": 4, "width": 23, "height": 30, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 7, -12.27, -33.25, 1, 1, 7, -12.27, -11.98, 1, 1, 7, 15.47, -11.98, 1, 1, 7, 15.47, -33.25, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"bones": {"bone3": {"translate": [{"y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 1.9333, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 2, "y": -1.32, "curve": "stepped"}, {"time": 2.3333, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 2.6, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 2.9333, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 3, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 3.2667, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 3.6, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 3.7667, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 4, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 4.1667, "y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "y": 0.58, "curve": 0.25, "c3": 0.75}, {"time": 4.3333, "y": -1.32}]}, "bone4": {"rotate": [{"angle": 9.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 19.01, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 9.39, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 19.01, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 9.39}, {"time": 2.3333, "angle": 19.01, "curve": "stepped"}, {"time": 4.3333, "angle": 19.01}, {"time": 4.6667, "angle": 9.39}]}, "bone5": {"rotate": [{"angle": -11.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -11.71, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -19.1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -11.71}, {"time": 2.3333, "angle": -19.1, "curve": "stepped"}, {"time": 4.3333, "angle": -19.1}, {"time": 4.6667, "angle": -11.71}]}, "bone8": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -2.84, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -2.84, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": "stepped"}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "x": -2.84, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "x": -2.84, "curve": 0.25, "c3": 0.75}, {"time": 4.3333}]}, "bone9": {"translate": [{"x": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -1.19, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.67, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -1.19, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.67}, {"time": 2.3333, "x": -3.21, "curve": "stepped"}, {"time": 4.3333, "x": -3.21}, {"time": 4.6667, "x": 1.67}]}, "bone6": {"translate": [{}, {"time": 2, "x": -2.36}, {"time": 2.3333, "x": -2.75, "curve": "stepped"}, {"time": 4.3333, "x": -2.75}, {"time": 4.6667}]}, "bone7": {"translate": [{}, {"time": 2, "x": -0.55}, {"time": 2.3333, "x": -0.65, "curve": "stepped"}, {"time": 4.3333, "x": -0.65}, {"time": 4.6667}]}}, "deform": {"default": {"2-6meimao1": {"2-6meimao1": [{"time": 2.3333, "vertices": [-6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198]}]}, "2-6meimao2": {"2-6meimao2": [{"time": 2.3333, "vertices": [-6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198]}]}, "2-6tou1": {"2-6tou1": [{"time": 2.3333, "vertices": [-6.28198, 0, -6.28198, 0, -6.28198, 0, 0.07908, 6.28145, 0.25269, -6.27687, -6.28198, 0, 0.07908, 6.28145, 0.25269, -6.27687, -6.28198, 0, 0.07908, 6.28145, 0.25269, -6.27687, -6.28198, 0, 0.25269, -6.27687, -6.28198, 0, 0.25269, -6.27687, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, 6.28143, 0.08319, 6.28143, 0.08319, 6.28143, 0.08319, -6.28198, 0, 6.28143, 0.08319, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, 0.07908, 6.28145, -6.28198, 0, 0.07908, 6.28145, -6.28198, 0, 0.07908, 6.28145, 0.25269, -6.27687, -6.28198, 0, 0.07908, 6.28145, 0.25269, -6.27687, -6.28198, 0, 0.07908, 6.28145, 0.25269, -6.27687, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198]}]}, "2-6yanjin1": {"2-6yanjin1": [{"time": 2.3333, "vertices": [-6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198]}]}, "2-6yanjin2": {"2-6yanjin2": [{"time": 2.3333, "vertices": [-6.28198, 0, -6.28198, 0, -6.28198, 0, -6.28198]}]}}}}}}