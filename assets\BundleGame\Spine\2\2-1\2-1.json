{"skeleton": {"hash": "Zn+VQXUDlC9HaYtMlDSGe0j6Ntc", "spine": "3.8.99", "x": -54.7, "y": -0.68, "width": 111, "height": 204.05, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/系列2/2-1"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "bone2", "parent": "bone", "length": 127.14, "rotation": 90, "x": -0.19, "y": 0.87}, {"name": "bone3", "parent": "bone2", "length": 72.38, "x": 90.78}, {"name": "bone4", "parent": "bone3", "x": 32.5, "y": -0.2}, {"name": "bone5", "parent": "bone3", "x": 65.86, "y": 0.04}, {"name": "bone6", "parent": "bone3", "length": 42.48, "rotation": 30.41, "x": 54.61, "y": 21.66}, {"name": "bone6b", "parent": "bone6", "length": 42.48, "x": 30.33}, {"name": "bone7", "parent": "bone3", "length": 40.88, "rotation": -34.02, "x": 57.56, "y": -20.34}, {"name": "bone7b", "parent": "bone7", "length": 40.88, "x": 29.19}], "slots": [{"name": "2-1<PERSON><PERSON>", "bone": "bone", "attachment": "2-1<PERSON><PERSON>"}, {"name": "2-1<PERSON><PERSON>", "bone": "bone", "attachment": "2-1<PERSON><PERSON>"}, {"name": "2-1yanjin2", "bone": "bone", "attachment": "2-1yanjin2"}, {"name": "2-1yanjin1", "bone": "bone", "attachment": "2-1yanjin1"}, {"name": "2-1meimao2", "bone": "bone", "attachment": "2-1meimao2"}, {"name": "2-1meimao1", "bone": "bone", "attachment": "2-1meimao1"}], "skins": [{"name": "default", "attachments": {"2-1meimao1": {"2-1meimao1": {"type": "mesh", "hull": 4, "width": 21, "height": 9, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 5, -10.24, 8.46, 1, 1, 5, -10.24, 29.46, 1, 1, 5, -1.24, 29.46, 1, 1, 5, -1.24, 8.46, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-1meimao2": {"2-1meimao2": {"type": "mesh", "hull": 4, "width": 21, "height": 9, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 5, -10.24, -29.54, 1, 1, 5, -10.24, -8.54, 1, 1, 5, -1.24, -8.54, 1, 1, 5, -1.24, -29.54, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-1shenti": {"2-1shenti": {"type": "mesh", "hull": 4, "width": 112, "height": 148, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -1.55, -45.67, 1, 1, 2, -1.55, 44.68, 1, 1, 2, 117.84, 44.68, 1, 1, 2, 117.84, -45.67, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-1tou": {"2-1tou": {"type": "mesh", "hull": 21, "width": 111, "height": 114, "uvs": [1, 0.11038, 0.97732, 0.22901, 0.94024, 0.33733, 0.87932, 0.43146, 1, 0.61184, 1, 1, 0, 1, 0, 0.57302, 0.10207, 0.44279, 0.0491, 0.33576, 0, 0.21584, 0.0385, 0.14105, 0.16564, 0.08689, 0.27556, 0.07142, 0.34707, 0.15524, 0.41858, 0.26355, 0.56943, 0.26125, 0.63962, 0.14778, 0.71378, 0.06009, 0.78927, 0, 0.93892, 0, 0.17491, 0.37574, 0.30999, 0.2945, 0.68067, 0.2922, 0.81576, 0.37214], "triangles": [1, 18, 0, 0, 18, 19, 0, 19, 20, 21, 11, 12, 10, 11, 9, 21, 9, 11, 14, 12, 13, 8, 3, 4, 24, 3, 16, 1, 24, 23, 8, 21, 3, 6, 7, 8, 3, 22, 15, 5, 6, 4, 3, 15, 16, 4, 6, 8, 3, 21, 22, 22, 14, 15, 16, 23, 24, 16, 17, 23, 8, 9, 21, 21, 12, 22, 3, 24, 2, 23, 17, 18, 22, 12, 14, 2, 24, 1, 1, 23, 18], "vertices": [3, 3, 99.14, -56.49, 0.00084, 8, 54.69, -6.69, 0.18313, 9, 25.5, -6.69, 0.81603, 3, 3, 85.62, -53.98, 0.11111, 8, 42.08, -12.17, 0.30112, 9, 12.89, -12.17, 0.58777, 3, 3, 73.27, -49.86, 0.44444, 8, 29.54, -15.67, 0.26094, 9, 0.35, -15.67, 0.29462, 1, 3, 62.54, -43.1, 1, 1, 3, 41.98, -56.49, 1, 1, 3, -2.27, -56.49, 1, 1, 3, -2.27, 54.51, 1, 1, 3, 46.4, 54.51, 1, 1, 3, 61.25, 43.18, 1, 2, 3, 73.45, 49.06, 0.44444, 6, 30.11, 14.09, 0.55556, 2, 3, 87.12, 54.51, 0.11425, 6, 44.66, 11.88, 0.88575, 2, 3, 95.65, 50.23, 0.0159, 6, 49.85, 3.87, 0.9841, 2, 3, 101.82, 36.12, 0.05159, 6, 48.04, -11.42, 0.94841, 2, 3, 103.58, 23.92, 0.19211, 6, 43.38, -22.84, 0.80789, 2, 3, 94.03, 15.98, 0.51268, 6, 31.12, -24.85, 0.48732, 1, 3, 81.68, 8.04, 1, 1, 3, 81.94, -8.7, 1, 3, 3, 94.88, -16.49, 0.48537, 8, 28.78, 24.07, 0.1842, 9, -0.41, 24.07, 0.33043, 3, 3, 104.88, -24.72, 0.15679, 8, 41.67, 22.85, 0.22533, 9, 12.48, 22.85, 0.61788, 3, 3, 111.73, -33.1, 0.02648, 8, 52.03, 19.73, 0.151, 9, 22.84, 19.73, 0.82252, 3, 3, 111.73, -49.71, 0.00559, 8, 61.33, 5.97, 0.09342, 9, 32.14, 5.97, 0.90099, 1, 3, 68.89, 35.09, 1, 1, 3, 78.15, 20.1, 1, 1, 3, 78.42, -21.05, 1, 1, 3, 69.3, -36.04, 1], "edges": [10, 12, 16, 42, 42, 44, 44, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 12, 14, 16, 14, 30, 32, 32, 46, 46, 48, 48, 6, 6, 4, 4, 2, 2, 0, 40, 0, 38, 40, 38, 36, 36, 34, 34, 32, 8, 10, 6, 8]}}, "2-1yanjin1": {"2-1yanjin1": {"type": "mesh", "hull": 4, "width": 19, "height": 21, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 4, 0.22, 12.71, 1, 1, 4, 0.22, 31.71, 1, 1, 4, 21.22, 31.71, 1, 1, 4, 21.22, 12.71, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-1yanjin2": {"2-1yanjin2": {"type": "mesh", "hull": 4, "width": 19, "height": 20, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 4, 0.22, -32.29, 1, 1, 4, 0.22, -13.29, 1, 1, 4, 20.22, -13.29, 1, 1, 4, 20.22, -32.29, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"bones": {"bone3": {"translate": [{"x": -5.64, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -13.71, "curve": "stepped"}, {"time": 0.2333, "x": -13.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -5.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -13.71, "curve": "stepped"}, {"time": 0.7333, "x": -13.71, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -5.64}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 0.98, "curve": "stepped"}, {"time": 0.2333, "x": 0.98, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 0.98, "curve": "stepped"}, {"time": 0.7333, "x": 0.98, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "bone6": {"rotate": [{"angle": -6.56, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "angle": -7.71, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.68, "curve": "stepped"}, {"time": 0.2667, "angle": 10.68, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "angle": -6.56, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.5333, "angle": -7.71, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 10.68, "curve": "stepped"}, {"time": 0.7667, "angle": 10.68, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1, "angle": -6.56}]}, "bone6b": {"rotate": [{"angle": -4.32, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": -7.71, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.68, "curve": "stepped"}, {"time": 0.3, "angle": 10.68, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -4.32, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5667, "angle": -7.71, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 10.68, "curve": "stepped"}, {"time": 0.8, "angle": 10.68, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1, "angle": -4.32}]}, "bone7": {"rotate": [{"angle": 8.87, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "angle": 10.12, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -9.82, "curve": "stepped"}, {"time": 0.2667, "angle": -9.82, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "angle": 8.87, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.5333, "angle": 10.12, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -9.82, "curve": "stepped"}, {"time": 0.7667, "angle": -9.82, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1, "angle": 8.87}]}, "bone7b": {"rotate": [{"angle": 6.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "angle": 10.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -9.82, "curve": "stepped"}, {"time": 0.3, "angle": -9.82, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": 6.44, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5667, "angle": 10.12, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -9.82, "curve": "stepped"}, {"time": 0.8, "angle": -9.82, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1, "angle": 6.44}]}, "bone4": {"translate": [{"x": 1.68, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "x": 1.97, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -2.63, "curve": "stepped"}, {"time": 0.2667, "x": -2.63, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "x": 1.68, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.5333, "x": 1.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -2.63, "curve": "stepped"}, {"time": 0.7667, "x": -2.63, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1, "x": 1.68}]}, "bone5": {"translate": [{"x": -0.11, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -5.28, "curve": "stepped"}, {"time": 0.3, "x": -5.28, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "x": -0.11, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5667, "x": 1.06, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -5.28, "curve": "stepped"}, {"time": 0.8, "x": -5.28, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1, "x": -0.11}]}}}}}