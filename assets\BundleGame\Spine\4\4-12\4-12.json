{"skeleton": {"hash": "0KgPKW/JVV2JU/JRMS6X9ZDhsIw", "spine": "3.8.99", "x": -79.28, "y": -3.03, "width": 163.84, "height": 153.83, "images": "./images/", "audio": "E:/外包/2.15音乐游戏/系列4/4-12"}, "bones": [{"name": "root", "scaleX": 0.9102, "scaleY": 0.9102}, {"name": "bone", "parent": "root"}, {"name": "bone2", "parent": "bone", "length": 42.49, "rotation": 90.97, "x": -0.15, "y": -0.05}, {"name": "bone3", "parent": "bone2", "x": 55.74, "y": -2.87}, {"name": "bone4", "parent": "bone3", "length": 24.75, "rotation": 178.48, "x": -11.43, "y": -0.28}, {"name": "bone5", "parent": "bone2", "x": 94.46, "y": -7.57}, {"name": "bone6", "parent": "bone2", "length": 40.44, "rotation": 41.17, "x": 124.8, "y": 26.43}, {"name": "bone7", "parent": "bone2", "length": 40.89, "rotation": -51.17, "x": 124.87, "y": -25.46}], "slots": [{"name": "4-12<PERSON><PERSON>", "bone": "root", "attachment": "4-12<PERSON><PERSON>"}, {"name": "4-12<PERSON><PERSON>", "bone": "root", "attachment": "4-12<PERSON><PERSON>"}, {"name": "4-12<PERSON><PERSON>", "bone": "root", "attachment": "4-12<PERSON><PERSON>"}], "skins": [{"name": "default", "attachments": {"4-12shenti": {"4-12shenti": {"type": "mesh", "hull": 33, "width": 180, "height": 169, "uvs": [0.95066, 0.08089, 0.9425, 0.17249, 0.86905, 0.23133, 0.78053, 0.21261, 0.75479, 0.17183, 0.71964, 0.19188, 0.68692, 0.23535, 0.76057, 0.30869, 0.80237, 0.41725, 0.89844, 0.43209, 1, 0.55863, 1, 1, 0, 1, 0, 0.46068, 0.15689, 0.42791, 0.19644, 0.32427, 0.26299, 0.24805, 0.20837, 0.18453, 0.16003, 0.22532, 0.07905, 0.22264, 0, 0.14976, 0, 0, 0.18389, 0, 0.23223, 0.06752, 0.31195, 0.11833, 0.35401, 0.18119, 0.48296, 0.14909, 0.5947, 0.17784, 0.62421, 0.13371, 0.68448, 0.07888, 0.74349, 0.06083, 0.7799, 0, 0.89541, 0, 0.73784, 0.12569], "triangles": [1, 2, 4, 3, 4, 2, 0, 1, 4, 31, 32, 30, 5, 28, 33, 5, 33, 4, 33, 28, 29, 32, 0, 4, 4, 33, 32, 30, 32, 33, 33, 29, 30, 18, 19, 17, 17, 19, 22, 17, 23, 24, 23, 17, 22, 22, 19, 20, 20, 21, 22, 16, 17, 24, 11, 12, 8, 8, 12, 14, 16, 8, 14, 16, 14, 15, 6, 8, 16, 6, 16, 27, 27, 25, 26, 27, 16, 25, 6, 7, 8, 11, 8, 10, 8, 9, 10, 12, 13, 14, 16, 24, 25, 27, 28, 6, 6, 28, 5], "vertices": [1, 7, 63.87, -18.37, 1, 1, 7, 52.83, -29.33, 1, 1, 7, 36.31, -28.5, 1, 2, 2, 128.87, -55.74, 0.02778, 7, 26.09, -15.87, 0.97222, 2, 2, 135.84, -51.22, 0.13194, 7, 26.95, -7.61, 0.86806, 2, 2, 132.56, -44.84, 0.50926, 7, 19.91, -6.16, 0.49074, 1, 2, 125.31, -38.82, 1, 1, 2, 112.69, -51.87, 1, 1, 2, 94.22, -59.08, 1, 1, 2, 91.42, -76.33, 1, 1, 2, 69.73, -94.24, 1, 1, 2, -4.85, -92.98, 1, 1, 2, -1.8, 86.99, 1, 1, 2, 89.33, 85.45, 1, 1, 2, 94.39, 57.12, 1, 1, 2, 111.78, 49.7, 1, 1, 2, 124.46, 37.51, 1, 2, 2, 135.36, 47.16, 0.3125, 6, 21.59, 8.65, 0.6875, 2, 2, 128.61, 55.97, 0.08333, 6, 22.32, 19.73, 0.91667, 1, 6, 32.44, 30.23, 1, 1, 6, 51.11, 32.52, 1, 1, 6, 69.88, 15.54, 1, 1, 6, 47.68, -9.01, 1, 2, 2, 155.06, 42.53, 0.14583, 6, 33.38, -7.8, 0.85417, 2, 2, 146.23, 28.33, 0.44444, 6, 17.38, -12.68, 0.55556, 1, 2, 135.48, 20.94, 1, 1, 2, 140.51, -2.36, 1, 1, 2, 135.31, -22.39, 1, 2, 2, 142.68, -27.83, 0.51852, 7, 13.01, 12.39, 0.48148, 2, 2, 151.76, -38.83, 0.18519, 7, 27.28, 12.56, 0.81481, 2, 2, 154.63, -49.51, 0.02778, 7, 37.39, 8.1, 0.97222, 1, 7, 49.01, 11.81, 1, 1, 7, 64.98, -1.51, 1, 2, 2, 143.69, -48.3, 0.02778, 7, 29.59, 0.33, 0.97222], "edges": [22, 24, 32, 30, 30, 28, 24, 26, 28, 26, 32, 34, 34, 36, 36, 38, 40, 42, 38, 40, 42, 44, 44, 46, 46, 48, 48, 50, 50, 32, 34, 46, 50, 52, 52, 54, 54, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 64, 62, 64, 62, 60, 60, 66, 66, 8, 60, 58, 58, 56, 56, 54, 12, 14, 14, 16, 16, 18, 22, 20, 18, 20]}}, "4-12yanjin": {"4-12yanjin": {"type": "mesh", "hull": 4, "width": 86, "height": 57, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 5, -28.5, -37.6, 1, 1, 5, -27.04, 48.38, 1, 1, 5, 29.95, 47.42, 1, 1, 5, 28.5, -38.57, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "4-12zuiba": {"4-12zuiba": {"type": "mesh", "hull": 11, "width": 61, "height": 56, "uvs": [1, 0.57143, 0.76498, 0.57568, 0.76797, 0.79219, 0.77083, 1, 0.50357, 1, 0.23436, 1, 0.23852, 0.77114, 0.24216, 0.57143, 0, 0.5693, 0, 0, 1, 0, 0.49967, 0.80093], "triangles": [11, 1, 2, 6, 7, 11, 5, 6, 11, 4, 11, 2, 5, 11, 4, 4, 2, 3, 1, 7, 10, 7, 8, 9, 10, 7, 9, 0, 1, 10, 11, 7, 1], "vertices": [1, 3, -16.59, -30.85, 1, 1, 3, -16.58, -16.51, 1, 2, 3, -28.71, -16.49, 0.44552, 4, 16.84, 16.66, 0.55448, 2, 3, -40.35, -16.47, 0.11218, 4, 28.48, 16.95, 0.88782, 2, 3, -40.07, -0.17, 0.00176, 4, 28.63, 0.65, 0.99824, 2, 3, -39.79, 16.25, 0.11355, 4, 28.79, -15.78, 0.88645, 2, 3, -26.98, 15.78, 0.44688, 4, 15.97, -15.64, 0.55312, 1, 3, -15.8, 15.37, 1, 1, 3, -15.43, 30.14, 1, 1, 3, 16.44, 29.6, 1, 1, 3, 15.41, -31.39, 1, 1, 4, 17.49, 0.3, 1], "edges": [18, 20, 16, 18, 16, 14, 14, 2, 0, 20, 2, 0, 10, 12, 12, 14, 6, 4, 4, 2, 6, 8, 8, 10]}}}}], "animations": {"animation": {"bones": {"bone5": {"scale": [{}, {"time": 0.1667, "x": 0.723}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5}, {"time": 0.6667, "x": 0.723}, {"time": 0.8333, "curve": "stepped"}, {"time": 1}, {"time": 1.1667, "x": 0.723}, {"time": 1.3333, "curve": "stepped"}, {"time": 1.5}, {"time": 1.6667, "x": 0.723}, {"time": 1.8333, "curve": "stepped"}, {"time": 2.3333}, {"time": 2.5, "x": 1.192, "y": 1.073}, {"time": 3.8333}, {"time": 4, "x": 1.192, "y": 1.073}, {"time": 5.3333}]}, "bone7": {"rotate": [{"time": 2.3333}, {"time": 2.5, "angle": -13.85}, {"time": 3.8333}, {"time": 4, "angle": -13.85}, {"time": 5.3333}]}, "bone4": {"scale": [{"time": 2.3333}, {"time": 2.5, "x": 1.39}, {"time": 3.3333, "curve": "stepped"}, {"time": 3.8333}, {"time": 4, "x": 1.39}, {"time": 4.8333}]}, "bone3": {"translate": [{"time": 2.3333}, {"time": 2.5, "x": -7.59, "y": 0.13}, {"time": 3.8333}, {"time": 4, "x": -7.59, "y": 0.13}, {"time": 5.3333}], "scale": [{"time": 2.3333}, {"time": 2.5, "x": 1.289}, {"time": 3.8333}, {"time": 4, "x": 1.289}, {"time": 5.3333}]}, "bone6": {"rotate": [{"time": 2.3333}, {"time": 2.5, "angle": 14.38}, {"time": 3.8333}, {"time": 4, "angle": 14.38}, {"time": 5.3333}]}}}}}