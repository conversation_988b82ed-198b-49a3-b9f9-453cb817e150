{"skeleton": {"hash": "FwAFzZJ4LXxJyZHhWPCiLLx2Ppc", "spine": "3.8.99", "x": -57.11, "y": -1.6, "width": 111.82, "height": 211.21, "images": "./images/", "audio": "E:/外包/2.15音乐游戏/系列3/3-11"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "scaleX": 0.9557, "scaleY": 0.9557}, {"name": "bone2", "parent": "bone", "length": 81.76, "rotation": 90, "x": -0.23, "y": -0.05}, {"name": "bone2b", "parent": "bone2", "length": 81.76, "x": 81.76}, {"name": "bone2b2", "parent": "bone2b", "length": 34.71, "rotation": 18.27, "x": 78.24, "y": 28.16}, {"name": "bone2b3", "parent": "bone2b2", "length": 27.23, "rotation": 5.28, "x": 34.71}, {"name": "bone2b4", "parent": "bone2b", "length": 29.48, "rotation": -20.99, "x": 77.28, "y": -26.56}, {"name": "bone2b5", "parent": "bone2b4", "length": 31.91, "rotation": -0.17, "x": 29.48}, {"name": "bone2b6", "parent": "bone2b", "x": 55.84, "y": -21.44}, {"name": "bone2b7", "parent": "bone2b", "x": 25.44, "y": 0.32}], "slots": [{"name": "3-11<PERSON><PERSON>", "bone": "bone", "attachment": "3-11<PERSON><PERSON>"}, {"name": "3-10<PERSON><PERSON>2", "bone": "bone", "attachment": "3-10<PERSON><PERSON>2"}, {"name": "3-10<PERSON>uo1", "bone": "bone", "attachment": "3-10<PERSON>uo1"}, {"name": "3-10<PERSON>u", "bone": "bone", "attachment": "3-10<PERSON>u"}, {"name": "3-10<PERSON><PERSON>", "bone": "bone", "attachment": "3-10<PERSON><PERSON>"}, {"name": "3-10<PERSON><PERSON>", "bone": "bone", "attachment": "3-10<PERSON><PERSON>"}], "skins": [{"name": "default", "attachments": {"3-10erduo1": {"3-10erduo1": {"type": "mesh", "hull": 10, "width": 37, "height": 64, "uvs": [0.67901, 0.18456, 0.90158, 0.41617, 1, 0.67923, 1, 1, 0.52074, 1, 0.24871, 0.85079, 0, 0.59059, 0, 0.2732, 0, 0, 0.35258, 0], "triangles": [7, 8, 9, 7, 9, 0, 1, 7, 0, 1, 6, 7, 5, 6, 1, 2, 5, 1, 4, 5, 2, 4, 2, 3], "vertices": [2, 4, 46.19, -11.84, 0.20668, 5, 10.34, -12.85, 0.79332, 2, 4, 29.53, -15.01, 0.95583, 5, -6.54, -14.47, 0.04417, 1, 4, 12.4, -13.19, 1, 1, 4, -7.09, -6.76, 1, 1, 4, -1.53, 10.08, 1, 1, 4, 10.69, 16.65, 1, 2, 4, 29.39, 20.16, 0.87248, 5, -3.44, 20.57, 0.12752, 2, 4, 48.68, 13.8, 0.06919, 5, 15.18, 12.45, 0.93081, 1, 5, 31.21, 5.47, 1, 2, 4, 61.19, -4.07, 0.00019, 5, 25.99, -6.49, 0.99981], "edges": [16, 18, 18, 0, 0, 2, 6, 4, 2, 4, 14, 16, 12, 14, 12, 10, 6, 8, 10, 8]}}, "3-10erduo2": {"3-10erduo2": {"type": "mesh", "hull": 10, "width": 37, "height": 64, "uvs": [1, 0.33321, 1, 0.61057, 0.74108, 0.85647, 0.45916, 1, 0, 1, 0, 0.77641, 0, 0.51049, 0.24649, 0.23885, 0.60754, 0, 0.82516, 0], "triangles": [8, 9, 0, 0, 7, 8, 1, 7, 0, 7, 1, 6, 1, 5, 6, 2, 5, 1, 3, 4, 5, 2, 3, 5], "vertices": [2, 6, 47.5, -14.89, 0.07844, 7, 18.07, -14.84, 0.92156, 2, 6, 30.93, -21.25, 0.65271, 7, 1.52, -21.25, 0.34729, 2, 6, 12.81, -17.94, 0.99814, 7, -16.62, -17.99, 0.00186, 1, 6, 0.49, -11.5, 1, 1, 6, -5.59, 4.37, 1, 1, 6, 7.77, 9.49, 1, 2, 6, 23.66, 15.59, 0.79235, 7, -5.87, 15.57, 0.20765, 2, 6, 43.15, 13.3, 0.02, 7, 13.64, 13.34, 0.98, 1, 7, 32.72, 6.4, 1, 1, 7, 35.62, -1.11, 1], "edges": [12, 14, 14, 16, 16, 18, 18, 0, 0, 2, 2, 4, 6, 8, 4, 6, 8, 10, 10, 12]}}, "3-10tou": {"3-10tou": {"type": "mesh", "hull": 4, "width": 117, "height": 88, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, 13.62, -57.48, 1, 1, 3, 13.62, 59.52, 1, 1, 3, 101.62, 59.52, 1, 1, 3, 101.62, -57.48, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "3-10yanjin": {"3-10yanjin": {"type": "mesh", "hull": 4, "width": 37, "height": 38, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 8, -18.22, -18.04, 1, 1, 8, -18.22, 18.96, 1, 1, 8, 19.78, 18.96, 1, 1, 8, 19.78, -18.04, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "3-10zuiba": {"3-10zuiba": {"type": "mesh", "hull": 4, "width": 52, "height": 22, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 9, -7.82, -24.8, 1, 1, 9, -7.82, 27.2, 1, 1, 9, 14.18, 27.2, 1, 1, 9, 14.18, -24.8, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "3-11shenti": {"3-11shenti": {"type": "mesh", "hull": 4, "width": 92, "height": 133, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -1.62, -44.48, 1, 1, 2, -1.62, 47.52, 1, 1, 2, 131.38, 47.52, 1, 1, 2, 131.38, -44.48, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"bones": {"bone2b": {"rotate": [{"angle": 14.52, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -9.74, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 14.52}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -4.3, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}]}, "bone2b2": {"rotate": [{"angle": 24.72, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "angle": 26.85, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -23.24, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.3333, "angle": 24.72}]}, "bone2b3": {"rotate": [{"angle": 20.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": 26.85, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -23.24, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "angle": 20.34}]}, "bone2b4": {"rotate": [{"angle": 23.69, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0667, "angle": 25.74, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -22.41, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.3333, "angle": 23.69}]}, "bone2b5": {"rotate": [{"angle": 19.48, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": 25.74, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -22.41, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.3333, "angle": 19.48}]}, "bone2b6": {"translate": [{"x": 0.62, "y": 2.38}, {"time": 0.6667, "x": -5.22, "y": -5.88}, {"time": 1.3333, "x": 0.62, "y": 2.38}]}, "bone2b7": {"scale": [{"y": 0.911, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 1.112, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 0.911, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 1.112, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "y": 0.911}]}}}}}