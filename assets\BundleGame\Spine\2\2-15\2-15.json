{"skeleton": {"hash": "DP2vBuqLcBLQ0KR81HRU+dJkFUo", "spine": "3.8.99", "x": -86.71, "y": -1.88, "width": 181.22, "height": 207.24, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/系列2/2-15"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "scaleX": 0.9293, "scaleY": 0.9293}, {"name": "bone2", "parent": "bone", "length": 117.05, "rotation": 90.53, "x": -0.26, "y": 0.52}, {"name": "bone8", "parent": "bone2", "length": 59.32, "x": 115.55, "y": -0.3}, {"name": "bone3", "parent": "bone8", "length": 21.67, "rotation": 34.16, "x": 46.24, "y": 25.49}, {"name": "bone4", "parent": "bone3", "length": 34.75, "rotation": -15.45, "x": 21.04}, {"name": "bone5", "parent": "bone8", "length": 28.42, "rotation": -39.19, "x": 49.8, "y": -20.68}, {"name": "bone6", "parent": "bone5", "length": 30.77, "rotation": 27.52, "x": 28.75, "y": 0.28}, {"name": "bone7", "parent": "bone8", "x": 25.59, "y": -0.49}], "slots": [{"name": "2-15<PERSON><PERSON><PERSON>", "bone": "bone", "attachment": "2-15<PERSON><PERSON><PERSON>"}, {"name": "2-15<PERSON><PERSON>2", "bone": "bone", "attachment": "2-15<PERSON><PERSON>2"}, {"name": "2-15<PERSON><PERSON>1", "bone": "bone", "attachment": "2-15<PERSON><PERSON>1"}, {"name": "2-15<PERSON><PERSON>", "bone": "bone", "attachment": "2-15<PERSON><PERSON>"}], "skins": [{"name": "default", "attachments": {"2-15shenti": {"2-15shenti": {"type": "mesh", "hull": 4, "width": 195, "height": 126, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -36.04, -102.4, 1, 1, 3, -34.23, 92.59, 1, 1, 3, 91.77, 91.42, 1, 1, 3, 89.95, -103.58, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-15shoubi": {"2-15shoubi": {"type": "mesh", "hull": 4, "width": 56, "height": 102, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -2.8, -27.93, 1, 1, 2, -2.28, 28.06, 1, 1, 2, 99.72, 27.11, 1, 1, 2, 99.2, -28.88, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "2-15yanjin1": {"2-15yanjin1": {"type": "mesh", "hull": 15, "width": 48, "height": 60, "uvs": [0.84489, 0.1628, 0.87883, 0.38302, 0.74308, 0.5429, 0.78913, 0.63459, 0.90075, 0.70869, 1, 0.76379, 1, 0.89489, 0.80338, 0.97089, 0.63475, 0.88349, 0.49225, 0.76759, 0.39993, 0.62435, 0.17368, 0.58815, 0, 0.41318, 0, 0, 0.65258, 0, 0.58093, 0.60022], "triangles": [6, 7, 4, 7, 8, 4, 4, 5, 6, 8, 9, 3, 8, 3, 4, 3, 9, 15, 9, 10, 15, 15, 2, 3, 14, 15, 10, 0, 2, 15, 10, 12, 14, 12, 10, 11, 2, 0, 1, 0, 15, 14, 12, 13, 14], "vertices": [1, 5, 22.55, -22.9, 1, 1, 5, 9.54, -20.08, 1, 2, 4, 20.71, -11.08, 0.19308, 5, 2.63, -10.77, 0.80692, 2, 4, 14.93, -9.77, 0.77435, 5, -3.29, -11.04, 0.22565, 2, 4, 8.22, -11.64, 0.98714, 5, -9.25, -14.64, 0.01286, 1, 4, 2.79, -13.68, 1, 1, 4, -3.67, -9.2, 1, 1, 4, -2.05, 1.15, 1, 1, 4, 6.87, 4.82, 1, 1, 4, 16.48, 6.49, 1, 2, 4, 26.07, 5.24, 0.69441, 5, 3.45, 6.39, 0.30559, 2, 4, 34.04, 12.93, 0.23441, 5, 9.08, 15.93, 0.76559, 2, 4, 47.41, 13.81, 0.07383, 5, 21.74, 20.34, 0.92617, 1, 5, 45.14, 12.16, 1, 1, 5, 34.82, -17.41, 1, 1, 5, 1.95, -2.29, 1], "edges": [24, 26, 24, 22, 22, 20, 20, 30, 30, 4, 4, 2, 2, 0, 26, 28, 0, 28, 4, 6, 6, 8, 8, 10, 20, 18, 18, 16, 16, 14, 10, 12, 14, 12]}}, "2-15yanjin2": {"2-15yanjin2": {"type": "mesh", "hull": 17, "width": 51, "height": 64, "uvs": [1, 0.17606, 1, 0.3659, 0.8997, 0.50887, 0.68499, 0.5909, 0.61146, 0.71981, 0.49088, 0.83934, 0.33499, 0.94715, 0.18794, 1, 0, 1, 0, 0.81356, 0.17323, 0.71746, 0.26735, 0.64012, 0.33499, 0.54168, 0.19382, 0.42684, 0.15852, 0.22059, 0.3997, 0, 0.72911, 0, 0.49676, 0.59559], "triangles": [10, 6, 7, 7, 8, 9, 7, 9, 10, 6, 10, 5, 10, 11, 5, 5, 11, 17, 5, 17, 4, 17, 11, 12, 4, 17, 3, 3, 17, 16, 1, 2, 3, 17, 15, 16, 17, 12, 15, 15, 12, 14, 1, 3, 16, 12, 13, 14, 16, 0, 1], "vertices": [2, 6, 61.9, -7.49, 0.1936, 7, 25.8, -22.2, 0.8064, 2, 6, 52.41, -15.08, 0.39319, 7, 13.88, -24.55, 0.60681, 2, 6, 42.07, -16.81, 0.62531, 7, 3.92, -21.3, 0.37469, 2, 6, 31.13, -11.53, 0.95553, 7, -3.35, -11.57, 0.04447, 1, 6, 22.34, -13.76, 1, 1, 6, 12.53, -13.74, 1, 1, 6, 2.17, -11.84, 1, 1, 6, -5.15, -8.1, 1, 1, 6, -11.14, -0.61, 1, 1, 6, -1.82, 6.84, 1, 1, 6, 8.5, 3.79, 1, 2, 6, 15.36, 3.13, 0.99917, 7, -10.56, 8.72, 0.00083, 2, 6, 22.44, 4.37, 0.64307, 7, -3.71, 6.55, 0.35693, 2, 6, 23.68, 14.59, 0.00731, 7, 2.11, 15.03, 0.99269, 1, 7, 14.71, 19.35, 1, 1, 7, 30.94, 10.01, 1, 2, 6, 62.06, 10.33, 0.01436, 7, 34.19, -6.47, 0.98564, 1, 6, 24.9, -4.23, 1], "edges": [24, 34, 34, 6, 6, 4, 4, 2, 24, 26, 26, 28, 28, 30, 30, 32, 2, 0, 32, 0, 24, 22, 22, 20, 16, 18, 20, 18, 6, 8, 8, 10, 10, 12, 14, 16, 12, 14]}}}}], "animations": {"animation": {"bones": {"bone2": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 0.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 0.97, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": "stepped"}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 0.97, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "bone3": {"rotate": [{"angle": -2.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": -2.37}]}, "bone4": {"rotate": [{"angle": -2.21, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": -11.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -11.99, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -11.99, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -11.99, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1, "angle": -2.21}]}, "bone5": {"rotate": [{"angle": 2.57, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "angle": 5.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 5.14, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 5.14, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 5.14, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": 2.57}]}, "bone6": {"rotate": [{"angle": 2.15, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": 11.65, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 11.65, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 11.65, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 11.65, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1, "angle": 2.15}]}, "bone7": {"translate": [{"x": 2.49, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "x": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -1.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -1.5, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -1.5, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "x": 2.49}]}, "bone8": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 0.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 0.97, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": "stepped"}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 0.97, "curve": 0.25, "c3": 0.75}, {"time": 1}]}}}}}